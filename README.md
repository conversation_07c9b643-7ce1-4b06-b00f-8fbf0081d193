# 🚀 أداة مراهنات الصاروخ - Rocket Betting Calculator

أداة احترافية مدعومة بالذكاء الصناعي لتوليد جداول مراهنات محسوبة بدقة عالية.

## ✨ المميزات الرئيسية

### 🎯 حساب دقيق ومتقدم
- حساب جداول المراهنات بناءً على الرصيد المتاح
- تحديد النسب والرهانات المثلى لكل جولة
- حساب الأرباح والخسائر المتوقعة
- تحليل مستوى المخاطرة

### 🤖 تحسين بالذكاء الصناعي
- تكامل مع Google Gemini AI
- تحليل ذكي للاستراتيجيات
- توصيات مخصصة حسب الرصيد والأهداف
- نظام دوران مفاتيح API متقدم

### 📊 إحصائيات شاملة
- إجمالي الرهانات والأرباح
- نسب الفوز والخسارة
- متوسط الرهانات
- مستوى المخاطرة
- الرصيد المتبقي

### 📁 تصدير متعدد الصيغ
- **Excel/CSV**: للتحليل المتقدم
- **PDF**: للطباعة والأرشفة
- **HTML**: للعرض التفاعلي
- **TXT**: للقراءة البسيطة
- **JSON**: للبيانات الخام

### ⚙️ إدارة الإعدادات
- حفظ واستيراد الإعدادات
- ملفات تعريفية متعددة
- التحقق من صحة البيانات
- توصيات تلقائية

## 🛠️ التقنيات المستخدمة

- **HTML5** - هيكل الصفحة
- **CSS3** - التصميم والتنسيق
- **JavaScript ES6+** - المنطق والتفاعل
- **Bootstrap 5.3** - التصميم المتجاوب
- **Google Fonts** - خطوط Cairo و Tajawal
- **Font Awesome** - الأيقونات
- **Google Gemini AI** - الذكاء الصناعي

## 📁 هيكل المشروع

```
Sugo Rocket/
├── index.html              # الصفحة الرئيسية
├── css/
│   ├── main.css           # الأنماط الرئيسية
│   └── components.css     # أنماط المكونات
├── js/
│   ├── calculator.js      # محرك الحسابات
│   ├── ai-integration.js  # تكامل الذكاء الصناعي
│   ├── export-system.js   # نظام التصدير
│   ├── settings-manager.js # إدارة الإعدادات
│   └── main.js           # الملف الرئيسي
├── assets/
│   └── images/           # الصور والأيقونات
└── README.md            # هذا الملف
```

## 🚀 كيفية الاستخدام

### 1. الإعداد الأولي
1. افتح ملف `index.html` في المتصفح
2. أدخل الإعدادات الأساسية:
   - الرصيد الإجمالي (افتراضي: 4000 جنيه)
   - عدد الجولات المطلوبة (افتراضي: 22 جولة)
   - النسبة الأولى (افتراضي: 5x)
   - زيادة النسبة كل 5 جولات

### 2. توليد الجدول
1. اضغط على زر "توليد الجدول"
2. انتظر اكتمال التحليل بالذكاء الصناعي
3. راجع النتائج والإحصائيات

### 3. التصدير والحفظ
- استخدم أزرار التصدير لحفظ النتائج
- صدّر الإعدادات لاستخدامها لاحقاً
- استورد إعدادات محفوظة مسبقاً

## ⚙️ الإعدادات المتقدمة

### أوضاع الذكاء الصناعي
- **محافظ**: مخاطرة منخفضة، أرباح مستقرة
- **متوازن**: توازن بين المخاطرة والربح
- **عدواني**: مخاطرة عالية، أرباح كبيرة محتملة

### خيارات الرهان
- الرهان الأولي: 10-1000 جنيه
- زيادة الرهان: 10, 20, 50, 100, 1000, 10000 جنيه
- النسبة الأولى: 1.1x - 50x

## 🔧 متطلبات النظام

- متصفح حديث يدعم ES6+
- اتصال بالإنترنت (للخطوط والذكاء الصناعي)
- JavaScript مفعل

## 🔑 مفاتيح API

التطبيق يستخدم 15 مفتاح API لـ Google Gemini مع نظام دوران ذكي:
- معالجة تلقائية لحدود الاستخدام
- إعادة المحاولة عند الفشل
- تسجيل مفصل للأخطاء

## 📊 الإحصائيات والتحليل

### المؤشرات الرئيسية
- **إجمالي الجولات**: عدد الجولات المحسوبة
- **إجمالي الرهانات**: مجموع المبالغ المراهن عليها
- **متوسط الرهان**: متوسط قيمة الرهان لكل جولة
- **نسبة الفوز**: النسبة المئوية للجولات الرابحة
- **مستوى المخاطرة**: تقييم المخاطرة (آمن - خطر جداً)
- **الرصيد المتبقي**: المبلغ المتبقي من الرصيد

### تحليل المخاطرة
- **آمن**: استخدام أقل من 25% من الرصيد
- **منخفض**: 25-50% من الرصيد
- **متوسط**: 50-75% من الرصيد
- **عالي**: 75-90% من الرصيد
- **عالي جداً**: 90%+ من الرصيد
- **خطر جداً**: تجاوز الرصيد المتاح

## 🎨 التصميم والواجهة

### الألوان والثيم
- تدرجات زرقاء وبنفسجية احترافية
- تصميم متجاوب يدعم جميع الأجهزة
- دعم كامل للغة العربية (RTL)
- أيقونات Font Awesome

### الخطوط
- **Cairo**: الخط الرئيسي للنصوص
- **Tajawal**: خط بديل للعناوين

## 🔍 البحث والتصفية

- بحث فوري في الجدول
- تصفية النتائج حسب الحالة
- تمييز الجولات الرابحة والخاسرة

## 📱 التوافق مع الأجهزة

- **سطح المكتب**: تجربة كاملة مع جميع المميزات
- **الأجهزة اللوحية**: واجهة محسنة للمس
- **الهواتف الذكية**: تصميم متجاوب مع قوائم قابلة للطي

## 🛡️ الأمان والخصوصية

- لا يتم حفظ البيانات على خوادم خارجية
- التخزين المحلي فقط في المتصفح
- مفاتيح API محمية ومشفرة
- لا يتم جمع بيانات شخصية

## 🔄 التحديثات المستقبلية

- [ ] دعم عملات إضافية
- [ ] تحليلات متقدمة بالذكاء الصناعي
- [ ] مشاركة الجداول عبر الروابط
- [ ] تطبيق موبايل مخصص
- [ ] تكامل مع منصات التداول

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- راجع التوثيق في هذا الملف
- تحقق من رسائل الخطأ في وحدة تحكم المتصفح
- تأكد من تفعيل JavaScript

## 📄 الترخيص

هذا المشروع مطور لأغراض تعليمية وتجريبية. يرجى استخدامه بمسؤولية.

---

**تطوير**: Augment Agent  
**التاريخ**: يونيو 2025  
**الإصدار**: 1.0.0

🚀 **استمتع بتجربة حساب مراهنات الصاروخ الاحترافية!**
