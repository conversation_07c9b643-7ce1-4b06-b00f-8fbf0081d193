/* ===== مكونات واجهة المستخدم المتخصصة ===== */

/* ===== مكون البروجريس المتقدم ===== */
.progress-container {
    position: relative;
    margin: 1rem 0;
}

.progress-circle {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: conic-gradient(var(--primary-color) 0deg, #e2e8f0 0deg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    position: relative;
}

.progress-circle::before {
    content: '';
    width: 90px;
    height: 90px;
    border-radius: 50%;
    background: white;
    position: absolute;
}

.progress-percentage {
    position: relative;
    z-index: 1;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

/* ===== مكون الجدول المتقدم ===== */
.advanced-table {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-heavy);
}

.table-header {
    background: var(--gradient-primary);
    color: white;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.table-controls {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.table-filter {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    border-radius: 6px;
    padding: 0.5rem;
    font-size: 0.875rem;
}

.table-filter::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

/* ===== صفوف الجدول المحسنة ===== */
.table-row {
    transition: var(--transition);
    border-bottom: 1px solid #e2e8f0;
}

.table-row:hover {
    background: linear-gradient(90deg, rgba(37, 99, 235, 0.05), rgba(37, 99, 235, 0.1));
    transform: scale(1.01);
}

.table-row.winning-row {
    background: linear-gradient(90deg, rgba(5, 150, 105, 0.1), rgba(5, 150, 105, 0.05));
    border-right: 4px solid var(--success-color);
}

.table-row.losing-row {
    background: linear-gradient(90deg, rgba(220, 38, 38, 0.1), rgba(220, 38, 38, 0.05));
    border-right: 4px solid var(--danger-color);
}

/* ===== خلايا الجدول المخصصة ===== */
.table-cell {
    padding: 1rem;
    text-align: center;
    vertical-align: middle;
    font-weight: 500;
}

.table-cell.round-number {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    font-weight: 700;
    border-radius: 8px;
    margin: 0.25rem;
    display: inline-block;
    min-width: 40px;
}

.table-cell.ratio {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--info-color);
}

.table-cell.bet-amount {
    font-weight: 700;
    color: var(--warning-color);
}

.table-cell.profit {
    font-weight: 700;
}

.table-cell.profit.positive {
    color: var(--success-color);
}

.table-cell.profit.negative {
    color: var(--danger-color);
}

/* ===== مؤشرات الحالة ===== */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
}

.status-indicator.success {
    background: rgba(5, 150, 105, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(5, 150, 105, 0.2);
}

.status-indicator.warning {
    background: rgba(217, 119, 6, 0.1);
    color: var(--warning-color);
    border: 1px solid rgba(217, 119, 6, 0.2);
}

.status-indicator.danger {
    background: rgba(220, 38, 38, 0.1);
    color: var(--danger-color);
    border: 1px solid rgba(220, 38, 38, 0.2);
}

/* ===== بطاقات الإحصائيات ===== */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin: 1.5rem 0;
}

.stat-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    text-align: center;
    box-shadow: var(--shadow-medium);
    transition: var(--transition);
    border-top: 4px solid var(--primary-color);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-heavy);
}

.stat-card.success {
    border-top-color: var(--success-color);
}

.stat-card.warning {
    border-top-color: var(--warning-color);
}

.stat-card.danger {
    border-top-color: var(--danger-color);
}

.stat-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.stat-card.success .stat-icon {
    color: var(--success-color);
}

.stat-card.warning .stat-icon {
    color: var(--warning-color);
}

.stat-card.danger .stat-icon {
    color: var(--danger-color);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.stat-description {
    color: var(--secondary-color);
    font-size: 0.875rem;
}

/* ===== أزرار التصدير المحسنة ===== */
.export-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.export-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    border: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: var(--transition);
    cursor: pointer;
}

.export-btn.excel {
    background: #217346;
    color: white;
}

.export-btn.pdf {
    background: #dc2626;
    color: white;
}

.export-btn.txt {
    background: #6b7280;
    color: white;
}

.export-btn.html {
    background: #ea580c;
    color: white;
}

.export-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
    opacity: 0.9;
}

/* ===== مكون التحميل المتقدم ===== */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
}

.loading-content {
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    box-shadow: var(--shadow-heavy);
    max-width: 400px;
    width: 90%;
}

.loading-spinner-large {
    width: 60px;
    height: 60px;
    border: 4px solid #e2e8f0;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

/* ===== تنبيهات مخصصة ===== */
.custom-alert {
    border-radius: var(--border-radius);
    border: none;
    padding: 1rem 1.5rem;
    margin: 1rem 0;
    box-shadow: var(--shadow-light);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.custom-alert.success {
    background: linear-gradient(135deg, rgba(5, 150, 105, 0.1), rgba(5, 150, 105, 0.05));
    border-right: 4px solid var(--success-color);
    color: var(--success-color);
}

.custom-alert.warning {
    background: linear-gradient(135deg, rgba(217, 119, 6, 0.1), rgba(217, 119, 6, 0.05));
    border-right: 4px solid var(--warning-color);
    color: var(--warning-color);
}

.custom-alert.danger {
    background: linear-gradient(135deg, rgba(220, 38, 38, 0.1), rgba(220, 38, 38, 0.05));
    border-right: 4px solid var(--danger-color);
    color: var(--danger-color);
}

.alert-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
}

.alert-content {
    flex: 1;
}

.alert-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.alert-message {
    font-size: 0.875rem;
    opacity: 0.8;
}

/* ===== مكون البحث المتقدم ===== */
.advanced-search {
    background: white;
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 1rem;
    box-shadow: var(--shadow-light);
}

.search-filters {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--dark-color);
}

.filter-input {
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 0.5rem;
    font-size: 0.875rem;
}

/* ===== تحسينات الاستجابة ===== */
@media (max-width: 768px) {
    .export-buttons {
        justify-content: center;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .table-controls {
        flex-direction: column;
        gap: 0.5rem;
        align-items: stretch;
    }
    
    .search-filters {
        grid-template-columns: 1fr;
    }
    
    .progress-circle {
        width: 80px;
        height: 80px;
    }
    
    .progress-circle::before {
        width: 60px;
        height: 60px;
    }
    
    .progress-percentage {
        font-size: 1rem;
    }
}

/* ===== تأثيرات خاصة ===== */
.glow-effect {
    box-shadow: 0 0 20px rgba(37, 99, 235, 0.3);
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from {
        box-shadow: 0 0 20px rgba(37, 99, 235, 0.3);
    }
    to {
        box-shadow: 0 0 30px rgba(37, 99, 235, 0.6);
    }
}

.bounce-in {
    animation: bounceIn 0.6s ease-out;
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}
