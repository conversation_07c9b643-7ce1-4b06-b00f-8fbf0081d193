<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أداة توليد جداول مراهنات الصاروخ بالذكاء الصناعي</title>
    <meta name="description" content="أداة احترافية مدعومة بالذكاء الصناعي لحساب جداول مراهنات الصاروخ بدقة عالية. احسب الرهانات والأرباح المثلى مع تحليل المخاطر المتقدم وتصدير النتائج بصيغ متعددة (Excel, PDF, HTML). تكامل مع Google Gemini AI لتحليل ذكي وتوصيات مخصصة.">
    <meta name="keywords" content="مراهنات الصاروخ, حاسبة الرهانات, ذكاء صناعي, جداول المراهنات, تحليل المخاطر, Gemini AI, تصدير Excel, حاسبة احترافية, استراتيجية الرهان, تحليل الأرباح, تعويض الخسائر, حساب النسب, إدارة الرصيد, تحسين الاستراتيجية">
    <meta name="author" content="أداة مراهنات الصاروخ الذكية - Powered by AI">
    <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1">
    <meta name="googlebot" content="index, follow">
    <meta name="language" content="Arabic">
    <meta name="geo.region" content="EG">
    <meta name="geo.placename" content="Egypt">
    <meta name="theme-color" content="#667eea">
    <meta name="msapplication-TileColor" content="#667eea">
    <meta name="application-name" content="أداة مراهنات الصاروخ">
    <meta name="apple-mobile-web-app-title" content="مراهنات الصاروخ">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="format-detection" content="telephone=no">
    <link rel="canonical" href="https://rocket-betting-calculator.com">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="أداة مراهنات الصاروخ - حاسبة احترافية مدعومة بالذكاء الصناعي">
    <meta property="og:description" content="احسب جداول المراهنات بدقة عالية مع تحليل ذكي للمخاطر والأرباح المتوقعة. تصدير بصيغ متعددة وإدارة متقدمة للإعدادات مع Google Gemini AI.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://rocket-betting-calculator.com">
    <meta property="og:image" content="https://rocket-betting-calculator.com/assets/images/og-image.jpg">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:locale" content="ar_EG">
    <meta property="og:site_name" content="أداة مراهنات الصاروخ">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="أداة مراهنات الصاروخ - حاسبة احترافية">
    <meta name="twitter:description" content="احسب جداول المراهنات بدقة عالية مع الذكاء الصناعي وتحليل المخاطر المتقدم">
    <meta name="twitter:image" content="https://rocket-betting-calculator.com/assets/images/twitter-card.jpg">
    <meta name="twitter:creator" content="@RocketBettingAI">
    
    <!-- Schema.org Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "أداة مراهنات الصاروخ - حاسبة احترافية مدعومة بالذكاء الصناعي",
        "description": "أداة احترافية مدعومة بالذكاء الصناعي لحساب جداول مراهنات الصاروخ بدقة عالية مع تحليل المخاطر وتصدير النتائج بصيغ متعددة",
        "applicationCategory": "FinanceApplication",
        "operatingSystem": "Web Browser",
        "url": "https://rocket-betting-calculator.com",
        "browserRequirements": "Requires JavaScript. Requires HTML5.",
        "softwareVersion": "1.0.0",
        "datePublished": "2025-06-29",
        "dateModified": "2025-06-29",
        "inLanguage": "ar",
        "isAccessibleForFree": true,
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD",
            "availability": "https://schema.org/InStock"
        },
        "author": {
            "@type": "Organization",
            "name": "أداة مراهنات الصاروخ الذكية",
            "description": "مطور أدوات مالية ذكية مدعومة بالذكاء الصناعي"
        },
        "featureList": [
            "حساب جداول المراهنات بدقة عالية",
            "تحليل المخاطر المتقدم",
            "تكامل مع Google Gemini AI",
            "تصدير بصيغ متعددة (Excel, PDF, HTML, TXT)",
            "إدارة الإعدادات والملفات التعريفية",
            "واجهة عربية متجاوبة",
            "حسابات في الوقت الفعلي"
        ],
        "applicationSubCategory": "Calculator",
        "keywords": "مراهنات الصاروخ, حاسبة الرهانات, ذكاء صناعي, تحليل المخاطر"
    }
    </script>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Preconnect for Performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdn.jsdelivr.net">
    <link rel="preconnect" href="https://generativelanguage.googleapis.com">

    <!-- Google Fonts - Cairo & Tajawal -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="css/main.css" rel="stylesheet">
    <link href="css/components.css" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="main-title">
                        <i class="fas fa-rocket text-primary me-3"></i>
                        أداة توليد جداول مراهنات الصاروخ
                    </h1>
                    <p class="subtitle">أداة احترافية مدعومة بالذكاء الصناعي لتوليد جداول مراهنات محسوبة بدقة</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="header-actions">
                        <button class="btn btn-outline-primary me-2" id="importSettings">
                            <i class="fas fa-upload"></i> استيراد الإعدادات
                        </button>
                        <input type="file" id="settingsFileInput" accept=".json" style="display: none;">
                        <button class="btn btn-primary" id="exportSettings">
                            <i class="fas fa-download"></i> تصدير الإعدادات
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Input Section -->
            <section class="input-section mb-5">
                <div class="card shadow-lg">
                    <div class="card-header bg-primary text-white">
                        <h3 class="card-title mb-0">
                            <i class="fas fa-cogs me-2"></i>
                            إعدادات المراهنة
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="totalBudget" class="form-label">
                                        <i class="fas fa-wallet text-success me-2"></i>
                                        إجمالي الرصيد (جنيه)
                                    </label>
                                    <input type="number" class="form-control" id="totalBudget" value="4000" min="100" max="1000000">
                                    <div class="form-text">الرصيد الإجمالي المتاح للمراهنة</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="targetRounds" class="form-label">
                                        <i class="fas fa-target text-warning me-2"></i>
                                        عدد الجولات المستهدف
                                    </label>
                                    <input type="number" class="form-control" id="targetRounds" value="22" min="5" max="50">
                                    <div class="form-text">العدد المطلوب من الجولات</div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="ratioIncrease" class="form-label">
                                        <i class="fas fa-chart-line text-info me-2"></i>
                                        زيادة النسبة كل كم جولة؟
                                    </label>
                                    <input type="number" class="form-control" id="ratioIncrease" value="5" min="1" max="10">
                                    <div class="form-text">عدد الجولات قبل زيادة النسبة</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="startingRatio" class="form-label">
                                        <i class="fas fa-play text-danger me-2"></i>
                                        بداية نسبة الرهان
                                    </label>
                                    <input type="number" class="form-control" id="startingRatio" value="5" min="2" max="20" step="0.1">
                                    <div class="form-text">النسبة الأولى للمراهنة (مثال: 5 = ×5)</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Advanced Options -->
                        <div class="advanced-options mt-4">
                            <button class="btn btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#advancedSettings">
                                <i class="fas fa-sliders-h me-2"></i>
                                خيارات متقدمة
                            </button>
                            <div class="collapse mt-3" id="advancedSettings">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group mb-3">
                                            <label for="initialBet" class="form-label">الرهان الأولي (جنيه)</label>
                                            <input type="number" class="form-control" id="initialBet" value="10" min="1" max="1000">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group mb-3">
                                            <label for="betIncrement" class="form-label">زيادة الرهان</label>
                                            <select class="form-select" id="betIncrement">
                                                <option value="10">+10 جنيه</option>
                                                <option value="100">+100 جنيه</option>
                                                <option value="1000">+1000 جنيه</option>
                                                <option value="10000">+10000 جنيه</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group mb-3">
                                            <label for="aiMode" class="form-label">وضع الذكاء الصناعي</label>
                                            <select class="form-select" id="aiMode">
                                                <option value="balanced">متوازن</option>
                                                <option value="conservative">محافظ</option>
                                                <option value="aggressive">عدواني</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Advanced Settings -->
                            <div class="card mt-4">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="fas fa-cogs me-2"></i>
                                        الإعدادات المتقدمة
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <!-- Custom Prompt -->
                                    <div class="form-group mb-3">
                                        <label for="customPrompt" class="form-label">
                                            <i class="fas fa-comment-dots me-1"></i>
                                            تعليمات إضافية للذكاء الصناعي (اختياري)
                                        </label>
                                        <textarea class="form-control" id="customPrompt" rows="3"
                                                  placeholder="أدخل تعليمات خاصة للذكاء الصناعي مثل: ركز على الأمان، تجنب المخاطر العالية، اعطي أولوية للأرباح السريعة..."></textarea>
                                    </div>

                                    <!-- Auto Adjustment -->
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-check mb-3">
                                                <input class="form-check-input" type="checkbox" id="autoAdjustment">
                                                <label class="form-check-label" for="autoAdjustment">
                                                    <i class="fas fa-sync-alt me-1"></i>
                                                    التعديل التلقائي عند عدم تحقيق أرباح
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="adjustmentThreshold" class="form-label">عدد الجولات للتعديل</label>
                                                <select class="form-select" id="adjustmentThreshold">
                                                    <option value="5">بعد 5 جولات</option>
                                                    <option value="10" selected>بعد 10 جولات</option>
                                                    <option value="15">بعد 15 جولة</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Exit Strategy -->
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="form-check mb-3">
                                                <input class="form-check-input" type="checkbox" id="exitStrategy">
                                                <label class="form-check-label" for="exitStrategy">
                                                    <i class="fas fa-door-open me-1"></i>
                                                    خطة انسحاب ذكية
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label for="exitAfterRounds" class="form-label">الانسحاب بعد جولات</label>
                                                <input type="number" class="form-control" id="exitAfterRounds"
                                                       value="20" min="5" max="50">
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label for="maxLossThreshold" class="form-label">حد الخسارة القصوى (جنيه)</label>
                                                <input type="number" class="form-control" id="maxLossThreshold"
                                                       value="2000" min="100" step="100">
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Exit Strategy Details -->
                                    <div id="exitStrategyDetails" style="display: none;">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="exitProfitReduction" class="form-label">تقليل نسبة الربح المستهدف (%)</label>
                                                    <select class="form-select" id="exitProfitReduction">
                                                        <option value="10">10%</option>
                                                        <option value="20" selected>20%</option>
                                                        <option value="30">30%</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="exitRounds" class="form-label">عدد جولات الانسحاب</label>
                                                    <select class="form-select" id="exitRounds">
                                                        <option value="5">5 جولات</option>
                                                        <option value="8" selected>8 جولات</option>
                                                        <option value="10">10 جولات</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Generate Button -->
                        <div class="text-center mt-4">
                            <button class="btn btn-success btn-lg px-5" id="generateTable">
                                <i class="fas fa-magic me-2"></i>
                                توليد الجدول بالذكاء الصناعي
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Progress Section -->
            <section class="progress-section mb-4" id="progressSection" style="display: none;">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="fas fa-cog fa-spin me-2"></i>
                            جاري التوليد...
                        </h5>
                        <div class="progress mb-3">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" id="progressBar" style="width: 0%"></div>
                        </div>
                        <div class="progress-status" id="progressStatus">بدء العملية...</div>
                        <div class="mt-3">
                            <button class="btn btn-warning me-2" id="pauseGeneration">
                                <i class="fas fa-pause"></i> إيقاف مؤقت
                            </button>
                            <button class="btn btn-danger" id="stopGeneration">
                                <i class="fas fa-stop"></i> إيقاف
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Results Section -->
            <section class="results-section" id="resultsSection" style="display: none;">
                <div class="card shadow-lg">
                    <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                        <h3 class="card-title mb-0">
                            <i class="fas fa-table me-2"></i>
                            جدول المراهنات المولد
                        </h3>
                        <div class="export-buttons">
                            <button class="btn btn-light btn-sm me-1" id="exportExcel">
                                <i class="fas fa-file-excel"></i> Excel
                            </button>
                            <button class="btn btn-light btn-sm me-1" id="exportPDF">
                                <i class="fas fa-file-pdf"></i> PDF
                            </button>
                            <button class="btn btn-light btn-sm me-1" id="exportTXT">
                                <i class="fas fa-file-alt"></i> TXT
                            </button>
                            <button class="btn btn-light btn-sm" id="exportHTML">
                                <i class="fas fa-file-code"></i> HTML
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Search Box -->
                        <div class="search-box mb-3">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" id="searchTable" placeholder="البحث في الجدول...">
                            </div>
                        </div>
                        
                        <!-- Table Container -->
                        <div class="table-responsive" id="tableContainer">
                            <!-- الجدول سيتم إدراجه هنا -->
                        </div>
                        
                        <!-- Summary Stats -->
                        <div class="summary-stats mt-4" id="summaryStats">
                            <!-- إحصائيات الجدول -->
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer-section mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2024 أداة مراهنات الصاروخ الذكية. جميع الحقوق محفوظة.</p>
                </div>
                <div class="col-md-6 text-end">
                    <p class="mb-0">مدعوم بالذكاء الصناعي من Google Gemini</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Hidden File Inputs -->
    <input type="file" id="settingsFileInput" accept=".json" style="display: none;">
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="js/calculator.js"></script>
    <script src="js/ai-integration.js"></script>
    <script src="js/export-system.js"></script>
    <script src="js/settings-manager.js"></script>
    <script src="js/main.js"></script>

    <script>
        // إظهار/إخفاء تفاصيل خطة الانسحاب
        document.getElementById('exitStrategy').addEventListener('change', function() {
            const details = document.getElementById('exitStrategyDetails');
            details.style.display = this.checked ? 'block' : 'none';
        });
    </script>
</body>
</html>
