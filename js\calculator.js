/**
 * ===== حاسبة جداول مراهنات الصاروخ =====
 * نظام حسابات متقدم لتوليد جداول مراهنات محسوبة بدقة
 */

class RocketBettingCalculator {
    constructor() {
        this.settings = {
            totalBudget: 4000,
            targetRounds: 22,
            ratioIncrease: 5,
            startingRatio: 5,
            initialBet: 10,
            betIncrement: 10,
            aiMode: 'balanced'
        };
        
        this.results = {
            table: [],
            statistics: {},
            isValid: false,
            errors: []
        };
    }

    /**
     * تحديث الإعدادات
     */
    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        this.validateSettings();
    }

    /**
     * التحقق من صحة الإعدادات
     */
    validateSettings() {
        const errors = [];
        
        if (this.settings.totalBudget < 100) {
            errors.push('الرصيد الإجمالي يجب أن يكون أكبر من 100 جنيه');
        }
        
        if (this.settings.targetRounds < 5 || this.settings.targetRounds > 50) {
            errors.push('عدد الجولات يجب أن يكون بين 5 و 50');
        }
        
        if (this.settings.startingRatio < 2) {
            errors.push('النسبة الأولى يجب أن تكون أكبر من 2');
        }
        
        if (this.settings.initialBet < 1) {
            errors.push('الرهان الأولي يجب أن يكون أكبر من 1 جنيه');
        }
        
        this.results.errors = errors;
        this.results.isValid = errors.length === 0;
        
        return this.results.isValid;
    }

    /**
     * حساب النسبة للجولة المحددة
     */
    calculateRatio(round) {
        const ratioLevel = Math.floor((round - 1) / this.settings.ratioIncrease);
        return this.settings.startingRatio + ratioLevel;
    }

    /**
     * حساب الرهان للجولة المحددة
     */
    calculateBet(round, previousLosses = 0) {
        let baseBet = this.settings.initialBet + ((round - 1) * this.settings.betIncrement);
        
        // تطبيق استراتيجية الذكاء الصناعي
        switch (this.settings.aiMode) {
            case 'conservative':
                // وضع محافظ - زيادة تدريجية
                baseBet = Math.min(baseBet, this.settings.totalBudget * 0.1);
                break;
                
            case 'aggressive':
                // وضع عدواني - زيادة أسرع
                if (round > 10) {
                    baseBet *= 1.5;
                }
                break;
                
            case 'balanced':
            default:
                // وضع متوازن - تعديل ذكي للرهان
                if (round > 15) {
                    // تعديل الرهان لضمان تغطية الخسائر
                    const targetProfit = Math.max(50, previousLosses * 0.1);
                    const ratio = this.calculateRatio(round);
                    const minBet = Math.ceil((previousLosses + targetProfit) / (ratio - 1));
                    baseBet = Math.max(baseBet, minBet);
                }
                break;
        }
        
        return Math.round(baseBet);
    }

    /**
     * توليد الجدول الكامل
     */
    generateTable() {
        if (!this.validateSettings()) {
            return this.results;
        }

        const table = [];
        let totalLosses = 0;
        let totalSpent = 0;

        for (let round = 1; round <= this.settings.targetRounds; round++) {
            const ratio = this.calculateRatio(round);
            const bet = this.calculateBet(round, totalLosses);
            const winAmount = bet * ratio;
            const netProfit = winAmount - bet - totalLosses;
            
            // التحقق من عدم تجاوز الرصيد
            if (totalSpent + bet > this.settings.totalBudget) {
                this.results.errors.push(`الرصيد غير كافي في الجولة ${round}`);
                break;
            }

            const roundData = {
                round: round,
                ratio: ratio,
                bet: bet,
                previousLosses: totalLosses,
                winAmount: winAmount,
                netProfit: netProfit,
                isWinning: netProfit > 0,
                cumulativeSpent: totalSpent + bet,
                remainingBudget: this.settings.totalBudget - (totalSpent + bet)
            };

            table.push(roundData);
            
            // تحديث الخسائر والمصروفات التراكمية
            totalLosses += bet;
            totalSpent += bet;
        }

        this.results.table = table;
        this.calculateStatistics();
        
        return this.results;
    }

    /**
     * حساب الإحصائيات
     */
    calculateStatistics() {
        const table = this.results.table;
        
        if (table.length === 0) {
            return;
        }

        const totalBets = table.reduce((sum, row) => sum + row.bet, 0);
        const averageBet = totalBets / table.length;
        const maxBet = Math.max(...table.map(row => row.bet));
        const minBet = Math.min(...table.map(row => row.bet));
        
        const winningRounds = table.filter(row => row.isWinning);
        const averageProfit = winningRounds.length > 0 
            ? winningRounds.reduce((sum, row) => sum + row.netProfit, 0) / winningRounds.length 
            : 0;
        
        const maxProfit = table.length > 0 ? Math.max(...table.map(row => row.netProfit)) : 0;
        const minProfit = table.length > 0 ? Math.min(...table.map(row => row.netProfit)) : 0;
        
        const budgetUtilization = (totalBets / this.settings.totalBudget) * 100;
        
        this.results.statistics = {
            totalRounds: table.length,
            totalBets: totalBets,
            averageBet: Math.round(averageBet),
            maxBet: maxBet,
            minBet: minBet,
            winningRounds: winningRounds.length,
            winningPercentage: (winningRounds.length / table.length) * 100,
            averageProfit: Math.round(averageProfit),
            maxProfit: maxProfit,
            minProfit: minProfit,
            budgetUtilization: Math.round(budgetUtilization * 100) / 100,
            remainingBudget: this.settings.totalBudget - totalBets,
            riskLevel: this.calculateRiskLevel(budgetUtilization)
        };
    }

    /**
     * حساب مستوى المخاطرة
     */
    calculateRiskLevel(budgetUtilization) {
        if (budgetUtilization < 50) return 'منخفض';
        if (budgetUtilization < 75) return 'متوسط';
        if (budgetUtilization < 90) return 'عالي';
        return 'خطر جداً';
    }

    /**
     * تحسين الجدول باستخدام الذكاء الصناعي
     */
    optimizeTable() {
        // خوارزمية تحسين متقدمة
        let bestTable = null;
        let bestScore = -Infinity;
        
        const modes = ['conservative', 'balanced', 'aggressive'];
        const increments = [10, 20, 50, 100];
        
        for (const mode of modes) {
            for (const increment of increments) {
                const tempSettings = {
                    ...this.settings,
                    aiMode: mode,
                    betIncrement: increment
                };
                
                const tempCalculator = new RocketBettingCalculator();
                tempCalculator.updateSettings(tempSettings);
                const result = tempCalculator.generateTable();
                
                if (result.isValid) {
                    const score = this.calculateTableScore(result);
                    if (score > bestScore) {
                        bestScore = score;
                        bestTable = result;
                    }
                }
            }
        }
        
        if (bestTable) {
            this.results = bestTable;
        }
        
        return this.results;
    }

    /**
     * حساب نقاط جودة الجدول
     */
    calculateTableScore(result) {
        const stats = result.statistics;
        
        // معايير التقييم
        const profitScore = stats.averageProfit * 0.3;
        const winningScore = stats.winningPercentage * 0.25;
        const budgetScore = (100 - stats.budgetUtilization) * 0.2;
        const riskScore = stats.riskLevel === 'منخفض' ? 100 : 
                         stats.riskLevel === 'متوسط' ? 70 : 
                         stats.riskLevel === 'عالي' ? 40 : 10;
        const roundsScore = (stats.totalRounds / this.settings.targetRounds) * 100 * 0.15;
        
        return profitScore + winningScore + budgetScore + (riskScore * 0.1) + (roundsScore * 0.1);
    }

    /**
     * تصدير البيانات
     */
    exportData(format = 'json') {
        switch (format) {
            case 'json':
                return JSON.stringify(this.results, null, 2);
                
            case 'csv':
                return this.exportToCSV();
                
            case 'html':
                return this.exportToHTML();
                
            default:
                return this.results;
        }
    }

    /**
     * تصدير إلى CSV
     */
    exportToCSV() {
        const headers = ['الجولة', 'النسبة', 'الرهان', 'الخسائر السابقة', 'الربح عند الفوز', 'الربح الصافي'];
        const rows = this.results.table.map(row => [
            row.round,
            row.ratio,
            row.bet,
            row.previousLosses,
            row.winAmount,
            row.netProfit
        ]);
        
        return [headers, ...rows].map(row => row.join(',')).join('\n');
    }

    /**
     * تصدير إلى HTML
     */
    exportToHTML() {
        let html = `
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>الجولة</th>
                    <th>النسبة</th>
                    <th>الرهان (جنيه)</th>
                    <th>الخسائر السابقة</th>
                    <th>الربح عند الفوز</th>
                    <th>الربح الصافي</th>
                    <th>الحالة</th>
                </tr>
            </thead>
            <tbody>
        `;
        
        this.results.table.forEach(row => {
            const statusIcon = row.isWinning ? '✅' : '❌';
            const profitClass = row.netProfit > 0 ? 'text-success' : 'text-danger';
            
            html += `
                <tr class="${row.isWinning ? 'table-success' : 'table-warning'}">
                    <td>${row.round}</td>
                    <td>${row.ratio}</td>
                    <td>${row.bet}</td>
                    <td>${row.previousLosses}</td>
                    <td>${row.winAmount}</td>
                    <td class="${profitClass}">${row.netProfit > 0 ? '+' : ''}${row.netProfit}</td>
                    <td>${statusIcon}</td>
                </tr>
            `;
        });
        
        html += '</tbody></table>';
        return html;
    }

    /**
     * استيراد الإعدادات
     */
    importSettings(settingsData) {
        try {
            const settings = typeof settingsData === 'string' 
                ? JSON.parse(settingsData) 
                : settingsData;
            
            this.updateSettings(settings);
            return true;
        } catch (error) {
            this.results.errors.push('خطأ في استيراد الإعدادات: ' + error.message);
            return false;
        }
    }

    /**
     * إعادة تعيين البيانات
     */
    reset() {
        this.results = {
            table: [],
            statistics: {},
            isValid: false,
            errors: []
        };
    }
}

// تصدير الكلاس للاستخدام العام
window.RocketBettingCalculator = RocketBettingCalculator;
