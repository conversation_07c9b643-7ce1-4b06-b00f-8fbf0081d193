/* ===== أداة مراهنات الصاروخ - التصميم الرئيسي ===== */

/* ===== المتغيرات العامة ===== */
:root {
    --primary-color: #2563eb;
    --secondary-color: #64748b;
    --success-color: #059669;
    --warning-color: #d97706;
    --danger-color: #dc2626;
    --info-color: #0891b2;
    --light-color: #f8fafc;
    --dark-color: #1e293b;
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-success: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    --gradient-warning: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --shadow-light: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-heavy: 0 10px 25px rgba(0, 0, 0, 0.15);
    --border-radius: 12px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== الإعدادات العامة ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', 'Tajawal', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: var(--dark-color);
    line-height: 1.6;
    direction: rtl;
}

/* ===== تحسين الخطوط ===== */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
    margin-bottom: 1rem;
}

p, span, div {
    font-family: 'Tajawal', sans-serif;
}

/* ===== الهيدر ===== */
.header-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 2rem 0;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-medium);
}

.main-title {
    font-size: 2.5rem;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
}

.subtitle {
    font-size: 1.1rem;
    color: var(--secondary-color);
    margin-bottom: 0;
}

.header-actions .btn {
    border-radius: var(--border-radius);
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: var(--transition);
    box-shadow: var(--shadow-light);
}

.header-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

/* ===== المحتوى الرئيسي ===== */
.main-content {
    padding: 2rem 0;
}

/* ===== البطاقات ===== */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-heavy);
    transition: var(--transition);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.card-header {
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
    border-bottom: none;
    padding: 1.5rem;
    background: var(--gradient-primary) !important;
}

.card-header.bg-success {
    background: var(--gradient-success) !important;
}

.card-body {
    padding: 2rem;
}

/* ===== النماذج ===== */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
}

.form-control, .form-select {
    border: 2px solid #e2e8f0;
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: var(--transition);
    background: rgba(255, 255, 255, 0.9);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
    background: white;
}

.form-text {
    color: var(--secondary-color);
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* ===== الأزرار ===== */
.btn {
    border-radius: var(--border-radius);
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: var(--transition);
    border: none;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-medium);
}

.btn-success {
    background: var(--gradient-success);
    color: white;
    box-shadow: var(--shadow-medium);
}

.btn-warning {
    background: var(--gradient-warning);
    color: white;
    box-shadow: var(--shadow-medium);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-heavy);
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

/* ===== شريط التقدم ===== */
.progress {
    height: 1rem;
    border-radius: var(--border-radius);
    background: rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.progress-bar {
    background: var(--gradient-success);
    transition: width 0.6s ease;
}

.progress-status {
    font-weight: 500;
    color: var(--secondary-color);
}

/* ===== الجداول ===== */
.table-responsive {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-light);
}

.table {
    margin-bottom: 0;
    background: white;
}

.table th {
    background: var(--gradient-primary);
    color: white;
    font-weight: 600;
    border: none;
    padding: 1rem;
    text-align: center;
}

.table td {
    padding: 0.75rem 1rem;
    border-color: #e2e8f0;
    text-align: center;
    vertical-align: middle;
}

.table tbody tr:hover {
    background: rgba(37, 99, 235, 0.05);
    transition: var(--transition);
}

/* ألوان محسنة للجدول */
.table-success-custom {
    background: linear-gradient(135deg, #d4edda, #c3e6cb) !important;
    border-left: 4px solid #28a745;
}

.table-success-custom:hover {
    background: linear-gradient(135deg, #c3e6cb, #b8dacc) !important;
}

.table-danger-custom {
    background: linear-gradient(135deg, #f8d7da, #f1b0b7) !important;
    border-left: 4px solid #dc3545;
}

.table-danger-custom:hover {
    background: linear-gradient(135deg, #f1b0b7, #e8a1aa) !important;
}

/* تحسين عرض الشارات */
.badge {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
}

.badge.bg-info {
    background: linear-gradient(135deg, #17a2b8, #138496) !important;
}

/* ===== البحث ===== */
.search-box .input-group {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-light);
}

.search-box .input-group-text {
    background: var(--primary-color);
    color: white;
    border: none;
}

.search-box .form-control {
    border: none;
    border-radius: 0;
}

/* ===== الإحصائيات ===== */
.summary-stats {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-top: 1rem;
}

.stat-item {
    text-align: center;
    padding: 1rem;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
}

.stat-label {
    color: var(--secondary-color);
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* ===== الفوتر ===== */
.footer-section {
    background: rgba(30, 41, 59, 0.95);
    color: white;
    padding: 2rem 0;
    margin-top: 3rem;
    backdrop-filter: blur(10px);
}

/* ===== الخيارات المتقدمة ===== */
.advanced-options {
    border-top: 1px solid #e2e8f0;
    padding-top: 1.5rem;
}

/* ===== الرسوم المتحركة ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

/* ===== الاستجابة للشاشات الصغيرة ===== */
@media (max-width: 768px) {
    .main-title {
        font-size: 1.8rem;
    }
    
    .header-actions {
        margin-top: 1rem;
        text-align: center !important;
    }
    
    .card-body {
        padding: 1.5rem;
    }
    
    .btn-lg {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

/* ===== تحسينات إضافية ===== */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.success-checkmark {
    color: var(--success-color);
    font-weight: bold;
}

.error-mark {
    color: var(--danger-color);
    font-weight: bold;
}

/* ===== تأثيرات التمرير ===== */
.scroll-to-top {
    position: fixed;
    bottom: 2rem;
    left: 2rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: none;
    z-index: 1000;
    transition: var(--transition);
}

.scroll-to-top:hover {
    background: var(--dark-color);
    transform: translateY(-3px);
}

/* ===== تنسيق التحليل المنسق ===== */
.formatted-content {
    line-height: 1.8;
    font-size: 0.95rem;
}

.formatted-content h6 {
    border-right: 3px solid var(--primary-color);
    padding-right: 10px;
    margin-top: 1rem;
    margin-bottom: 0.5rem;
}

.formatted-content .badge {
    font-size: 0.8rem;
    margin-left: 5px;
}

.formatted-content p {
    text-align: justify;
    margin-bottom: 0.8rem;
}

.analysis-section,
.recommendations-section {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 10px;
    padding: 1.5rem;
    border: 1px solid #dee2e6;
    height: 100%;
}

.ai-analysis-section .card {
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    border: none;
    overflow: hidden;
}

.ai-analysis-section .card-header {
    border: none;
    padding: 1.5rem;
}

.ai-analysis-section .card-body {
    padding: 2rem;
}

/* تحسين عرض الشارات والعناصر المميزة */
.formatted-content .text-success {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    padding: 2px 6px;
    border-radius: 4px;
    border: 1px solid #b8dacc;
}

.formatted-content .text-danger {
    background: linear-gradient(135deg, #f8d7da, #f1b0b7);
    padding: 2px 6px;
    border-radius: 4px;
    border: 1px solid #f1b0b7;
}

.formatted-content .text-info {
    background: linear-gradient(135deg, #d1ecf1, #bee5eb);
    padding: 2px 6px;
    border-radius: 4px;
    border: 1px solid #bee5eb;
}

/* ===== شبكة الإحصائيات المحسنة ===== */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    text-align: center;
    transition: all 0.3s ease;
    border-left: 4px solid var(--primary-color);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.stat-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.stat-card.success {
    border-left-color: #28a745;
    background: linear-gradient(135deg, #f8fff9, #e8f5e8);
}

.stat-card.success::before {
    background: linear-gradient(90deg, #28a745, #20c997);
}

.stat-card.danger {
    border-left-color: #dc3545;
    background: linear-gradient(135deg, #fff8f8, #f5e8e8);
}

.stat-card.danger::before {
    background: linear-gradient(90deg, #dc3545, #e74c3c);
}

.stat-number {
    font-size: 2.2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin: 0.5rem 0;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stat-description {
    color: var(--text-muted);
    font-size: 0.9rem;
    font-weight: 500;
    margin-top: 0.5rem;
}

.stat-icon {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    opacity: 0.8;
}
