<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار أداة مراهنات الصاروخ</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background: #f8f9fa; }
        .test-section { margin: 2rem 0; padding: 2rem; background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-result { padding: 1rem; margin: 1rem 0; border-radius: 5px; }
        .test-pass { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .test-fail { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .test-pending { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="text-center mb-4">
            <h1>🧪 اختبار أداة مراهنات الصاروخ</h1>
            <p class="text-muted">اختبار شامل لجميع وظائف الأداة</p>
        </div>

        <div class="test-section">
            <h3>📋 قائمة الاختبارات</h3>
            <div id="testResults">
                <div class="test-result test-pending" id="test-1">
                    ⏳ اختبار تحميل الملفات الأساسية
                </div>
                <div class="test-result test-pending" id="test-2">
                    ⏳ اختبار تهيئة الكلاسات الرئيسية
                </div>
                <div class="test-result test-pending" id="test-3">
                    ⏳ اختبار حساب الجدول الأساسي
                </div>
                <div class="test-result test-pending" id="test-4">
                    ⏳ اختبار نظام التصدير
                </div>
                <div class="test-result test-pending" id="test-5">
                    ⏳ اختبار إدارة الإعدادات
                </div>
                <div class="test-result test-pending" id="test-6">
                    ⏳ اختبار واجهة المستخدم
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🎮 اختبار تفاعلي</h3>
            <div class="row">
                <div class="col-md-6">
                    <button class="btn btn-primary w-100 mb-2" onclick="testCalculation()">
                        اختبار الحسابات
                    </button>
                    <button class="btn btn-success w-100 mb-2" onclick="testExport()">
                        اختبار التصدير
                    </button>
                    <button class="btn btn-info w-100 mb-2" onclick="testSettings()">
                        اختبار الإعدادات
                    </button>
                </div>
                <div class="col-md-6">
                    <div id="interactiveResults" class="border p-3 rounded">
                        <p class="text-muted">اضغط على الأزرار لبدء الاختبارات التفاعلية</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>📊 نتائج الاختبار</h3>
            <div id="finalResults">
                <div class="alert alert-info">
                    <strong>الحالة:</strong> جاري تشغيل الاختبارات...
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل الملفات المطلوبة -->
    <script src="js/calculator.js"></script>
    <script src="js/ai-integration.js"></script>
    <script src="js/export-system.js"></script>
    <script src="js/settings-manager.js"></script>
    <script src="js/main.js"></script>

    <script>
        // متغيرات الاختبار
        let testResults = {
            filesLoaded: false,
            classesInitialized: false,
            calculationWorks: false,
            exportWorks: false,
            settingsWork: false,
            uiResponsive: false
        };

        // بدء الاختبارات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(runTests, 1000);
        });

        // تشغيل جميع الاختبارات
        async function runTests() {
            await testFileLoading();
            await testClassInitialization();
            await testBasicCalculation();
            await testExportSystem();
            await testSettingsManager();
            await testUIResponsiveness();
            
            updateFinalResults();
        }

        // اختبار تحميل الملفات
        async function testFileLoading() {
            try {
                const requiredClasses = [
                    'RocketBettingCalculator',
                    'GeminiAIIntegration', 
                    'ExportSystem',
                    'SettingsManager',
                    'RocketBettingApp'
                ];
                
                let allLoaded = true;
                for (let className of requiredClasses) {
                    if (typeof window[className] === 'undefined') {
                        allLoaded = false;
                        break;
                    }
                }
                
                testResults.filesLoaded = allLoaded;
                updateTestResult('test-1', allLoaded, 'تحميل الملفات الأساسية');
            } catch (error) {
                testResults.filesLoaded = false;
                updateTestResult('test-1', false, 'تحميل الملفات الأساسية', error.message);
            }
        }

        // اختبار تهيئة الكلاسات
        async function testClassInitialization() {
            try {
                const calculator = new RocketBettingCalculator();
                const exportSystem = new ExportSystem();
                const settingsManager = new SettingsManager();
                
                const initialized = calculator && exportSystem && settingsManager;
                testResults.classesInitialized = initialized;
                updateTestResult('test-2', initialized, 'تهيئة الكلاسات الرئيسية');
            } catch (error) {
                testResults.classesInitialized = false;
                updateTestResult('test-2', false, 'تهيئة الكلاسات الرئيسية', error.message);
            }
        }

        // اختبار الحساب الأساسي
        async function testBasicCalculation() {
            try {
                const calculator = new RocketBettingCalculator();
                const testSettings = {
                    totalBudget: 1000,
                    targetRounds: 5,
                    ratioIncrease: 5,
                    startingRatio: 2,
                    initialBet: 10,
                    betIncrement: 10
                };
                
                calculator.updateSettings(testSettings);
                const result = calculator.generateTable();
                
                const works = result && result.isValid && result.table && result.table.length > 0;
                testResults.calculationWorks = works;
                updateTestResult('test-3', works, 'حساب الجدول الأساسي');
            } catch (error) {
                testResults.calculationWorks = false;
                updateTestResult('test-3', false, 'حساب الجدول الأساسي', error.message);
            }
        }

        // اختبار نظام التصدير
        async function testExportSystem() {
            try {
                const exportSystem = new ExportSystem();
                const testData = {
                    table: [
                        { round: 1, ratio: 2, bet: 10, previousLosses: 0, winAmount: 20, netProfit: 10, isWinning: true }
                    ],
                    statistics: {
                        totalRounds: 1,
                        totalBets: 10,
                        averageBet: 10,
                        winningPercentage: 100,
                        riskLevel: 'منخفض',
                        remainingBudget: 990
                    }
                };
                
                // اختبار توليد المحتوى فقط (بدون تحميل)
                const csvContent = exportSystem.generateCSVContent(testData);
                const txtContent = exportSystem.generateTXTContent(testData);
                
                const works = csvContent && txtContent && csvContent.length > 0 && txtContent.length > 0;
                testResults.exportWorks = works;
                updateTestResult('test-4', works, 'نظام التصدير');
            } catch (error) {
                testResults.exportWorks = false;
                updateTestResult('test-4', false, 'نظام التصدير', error.message);
            }
        }

        // اختبار إدارة الإعدادات
        async function testSettingsManager() {
            try {
                const settingsManager = new SettingsManager();
                const testSettings = { totalBudget: 2000, targetRounds: 10 };
                
                const validation = settingsManager.updateSettings(testSettings);
                const currentSettings = settingsManager.getCurrentSettings();
                
                const works = validation && currentSettings && currentSettings.totalBudget === 2000;
                testResults.settingsWork = works;
                updateTestResult('test-5', works, 'إدارة الإعدادات');
            } catch (error) {
                testResults.settingsWork = false;
                updateTestResult('test-5', false, 'إدارة الإعدادات', error.message);
            }
        }

        // اختبار واجهة المستخدم
        async function testUIResponsiveness() {
            try {
                // التحقق من وجود العناصر الأساسية
                const requiredElements = [
                    'totalBudget',
                    'targetRounds', 
                    'generateTable',
                    'progressSection',
                    'resultsSection'
                ];
                
                let allElementsExist = true;
                for (let elementId of requiredElements) {
                    if (!document.getElementById(elementId)) {
                        allElementsExist = false;
                        break;
                    }
                }
                
                testResults.uiResponsive = allElementsExist;
                updateTestResult('test-6', allElementsExist, 'واجهة المستخدم');
            } catch (error) {
                testResults.uiResponsive = false;
                updateTestResult('test-6', false, 'واجهة المستخدم', error.message);
            }
        }

        // تحديث نتيجة اختبار محدد
        function updateTestResult(testId, passed, testName, errorMessage = '') {
            const element = document.getElementById(testId);
            if (passed) {
                element.className = 'test-result test-pass';
                element.innerHTML = `✅ ${testName} - نجح`;
            } else {
                element.className = 'test-result test-fail';
                element.innerHTML = `❌ ${testName} - فشل${errorMessage ? ': ' + errorMessage : ''}`;
            }
        }

        // تحديث النتائج النهائية
        function updateFinalResults() {
            const totalTests = Object.keys(testResults).length;
            const passedTests = Object.values(testResults).filter(result => result).length;
            const percentage = Math.round((passedTests / totalTests) * 100);
            
            let alertClass = 'alert-success';
            let status = 'ممتاز';
            
            if (percentage < 100) {
                alertClass = percentage >= 80 ? 'alert-warning' : 'alert-danger';
                status = percentage >= 80 ? 'جيد مع تحذيرات' : 'يحتاج إصلاح';
            }
            
            document.getElementById('finalResults').innerHTML = `
                <div class="alert ${alertClass}">
                    <h5><strong>نتيجة الاختبار النهائية:</strong> ${status}</h5>
                    <p><strong>نجح:</strong> ${passedTests}/${totalTests} اختبار (${percentage}%)</p>
                    <div class="progress mt-2">
                        <div class="progress-bar ${percentage >= 80 ? 'bg-success' : 'bg-danger'}" 
                             style="width: ${percentage}%">${percentage}%</div>
                    </div>
                </div>
            `;
        }

        // اختبارات تفاعلية
        function testCalculation() {
            const results = document.getElementById('interactiveResults');
            results.innerHTML = '<p class="text-info">جاري اختبار الحسابات...</p>';
            
            try {
                const calculator = new RocketBettingCalculator();
                const result = calculator.generateTable();
                
                if (result && result.isValid) {
                    results.innerHTML = `
                        <div class="alert alert-success">
                            <strong>✅ نجح اختبار الحسابات</strong><br>
                            تم توليد ${result.table.length} جولة<br>
                            إجمالي الرهانات: ${result.statistics.totalBets} جنيه
                        </div>
                    `;
                } else {
                    results.innerHTML = `
                        <div class="alert alert-danger">
                            <strong>❌ فشل اختبار الحسابات</strong><br>
                            ${result.errors ? result.errors.join(', ') : 'خطأ غير محدد'}
                        </div>
                    `;
                }
            } catch (error) {
                results.innerHTML = `
                    <div class="alert alert-danger">
                        <strong>❌ خطأ في اختبار الحسابات</strong><br>
                        ${error.message}
                    </div>
                `;
            }
        }

        function testExport() {
            const results = document.getElementById('interactiveResults');
            results.innerHTML = '<p class="text-info">جاري اختبار التصدير...</p>';
            
            try {
                const exportSystem = new ExportSystem();
                const testData = {
                    table: [{ round: 1, ratio: 2, bet: 10, previousLosses: 0, winAmount: 20, netProfit: 10, isWinning: true }],
                    statistics: { totalRounds: 1, totalBets: 10, averageBet: 10, winningPercentage: 100, riskLevel: 'منخفض', remainingBudget: 990 }
                };
                
                const csvContent = exportSystem.generateCSVContent(testData);
                
                results.innerHTML = `
                    <div class="alert alert-success">
                        <strong>✅ نجح اختبار التصدير</strong><br>
                        تم توليد محتوى CSV بحجم ${csvContent.length} حرف
                    </div>
                `;
            } catch (error) {
                results.innerHTML = `
                    <div class="alert alert-danger">
                        <strong>❌ فشل اختبار التصدير</strong><br>
                        ${error.message}
                    </div>
                `;
            }
        }

        function testSettings() {
            const results = document.getElementById('interactiveResults');
            results.innerHTML = '<p class="text-info">جاري اختبار الإعدادات...</p>';
            
            try {
                const settingsManager = new SettingsManager();
                const testSettings = { totalBudget: 5000, targetRounds: 15 };
                
                const validation = settingsManager.updateSettings(testSettings);
                const current = settingsManager.getCurrentSettings();
                
                if (validation.isValid && current.totalBudget === 5000) {
                    results.innerHTML = `
                        <div class="alert alert-success">
                            <strong>✅ نجح اختبار الإعدادات</strong><br>
                            تم تحديث الرصيد إلى ${current.totalBudget} جنيه<br>
                            عدد الجولات: ${current.targetRounds}
                        </div>
                    `;
                } else {
                    results.innerHTML = `
                        <div class="alert alert-warning">
                            <strong>⚠️ تحذيرات في الإعدادات</strong><br>
                            ${validation.errors ? validation.errors.join(', ') : 'تحذيرات متنوعة'}
                        </div>
                    `;
                }
            } catch (error) {
                results.innerHTML = `
                    <div class="alert alert-danger">
                        <strong>❌ فشل اختبار الإعدادات</strong><br>
                        ${error.message}
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
