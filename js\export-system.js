/**
 * ===== نظام التصدير المتقدم =====
 * نظام شامل لتصدير البيانات بصيغ متعددة
 */

class ExportSystem {
    constructor() {
        this.supportedFormats = ['excel', 'pdf', 'txt', 'html', 'json'];
        this.exportHistory = [];
    }

    /**
     * تصدير البيانات بالصيغة المحددة
     */
    async exportData(data, format, filename = null) {
        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const defaultFilename = `rocket-betting-table-${timestamp}`;
            const finalFilename = filename || defaultFilename;

            let result;
            switch (format.toLowerCase()) {
                case 'excel':
                    result = await this.exportToExcel(data, finalFilename);
                    break;
                case 'pdf':
                    result = await this.exportToPDF(data, finalFilename);
                    break;
                case 'txt':
                    result = await this.exportToTXT(data, finalFilename);
                    break;
                case 'html':
                    result = await this.exportToHTML(data, finalFilename);
                    break;
                case 'json':
                    result = await this.exportToJSON(data, finalFilename);
                    break;
                default:
                    throw new Error(`صيغة غير مدعومة: ${format}`);
            }

            // تسجيل عملية التصدير
            this.recordExport(format, finalFilename, data);
            
            return result;
        } catch (error) {
            console.error('خطأ في التصدير:', error);
            throw error;
        }
    }

    /**
     * تصدير إلى Excel
     */
    async exportToExcel(data, filename) {
        // إنشاء محتوى CSV متوافق مع Excel
        const csvContent = this.generateCSVContent(data);
        
        // تحويل إلى Blob مع ترميز UTF-8 BOM للدعم العربي
        const BOM = '\uFEFF';
        const blob = new Blob([BOM + csvContent], { 
            type: 'text/csv;charset=utf-8;' 
        });
        
        this.downloadBlob(blob, `${filename}.csv`);
        
        return {
            success: true,
            format: 'excel',
            filename: `${filename}.csv`,
            size: blob.size
        };
    }

    /**
     * تصدير إلى PDF
     */
    async exportToPDF(data, filename) {
        // إنشاء محتوى HTML للطباعة
        const htmlContent = this.generatePDFHTML(data);
        
        // فتح نافذة طباعة
        const printWindow = window.open('', '_blank');
        printWindow.document.write(htmlContent);
        printWindow.document.close();
        
        // تأخير قصير للسماح بتحميل المحتوى
        setTimeout(() => {
            printWindow.print();
        }, 500);
        
        return {
            success: true,
            format: 'pdf',
            filename: `${filename}.pdf`,
            note: 'يرجى استخدام خيار "حفظ كـ PDF" في نافذة الطباعة'
        };
    }

    /**
     * تصدير إلى TXT
     */
    async exportToTXT(data, filename) {
        const txtContent = this.generateTXTContent(data);
        
        const blob = new Blob([txtContent], { 
            type: 'text/plain;charset=utf-8' 
        });
        
        this.downloadBlob(blob, `${filename}.txt`);
        
        return {
            success: true,
            format: 'txt',
            filename: `${filename}.txt`,
            size: blob.size
        };
    }

    /**
     * تصدير إلى HTML
     */
    async exportToHTML(data, filename) {
        const htmlContent = this.generateCompleteHTML(data);
        
        const blob = new Blob([htmlContent], { 
            type: 'text/html;charset=utf-8' 
        });
        
        this.downloadBlob(blob, `${filename}.html`);
        
        return {
            success: true,
            format: 'html',
            filename: `${filename}.html`,
            size: blob.size
        };
    }

    /**
     * تصدير إلى JSON
     */
    async exportToJSON(data, filename) {
        const jsonContent = JSON.stringify(data, null, 2);
        
        const blob = new Blob([jsonContent], { 
            type: 'application/json;charset=utf-8' 
        });
        
        this.downloadBlob(blob, `${filename}.json`);
        
        return {
            success: true,
            format: 'json',
            filename: `${filename}.json`,
            size: blob.size
        };
    }

    /**
     * إنشاء محتوى CSV
     */
    generateCSVContent(data) {
        const headers = [
            'الجولة',
            'النسبة',
            'الرهان (جنيه)',
            'مجموع الخسائر السابقة',
            'الربح عند الفوز',
            'الربح الصافي',
            'الحالة'
        ];
        
        let csv = headers.join(',') + '\n';
        
        data.table.forEach(row => {
            const status = row.isWinning ? 'ربح ✅' : 'خسارة ❌';
            const csvRow = [
                row.round,
                row.ratio,
                row.bet,
                row.previousLosses,
                row.winAmount,
                row.netProfit,
                status
            ];
            csv += csvRow.join(',') + '\n';
        });
        
        // إضافة الإحصائيات
        csv += '\n--- الإحصائيات ---\n';
        csv += `إجمالي الجولات,${data.statistics.totalRounds}\n`;
        csv += `إجمالي الرهانات,${data.statistics.totalBets}\n`;
        csv += `متوسط الرهان,${data.statistics.averageBet}\n`;
        csv += `أعلى رهان,${data.statistics.maxBet}\n`;
        csv += `نسبة الفوز,${data.statistics.winningPercentage.toFixed(2)}%\n`;
        csv += `مستوى المخاطرة,${data.statistics.riskLevel}\n`;
        
        return csv;
    }

    /**
     * إنشاء محتوى TXT
     */
    generateTXTContent(data) {
        let content = '===== جدول مراهنات الصاروخ =====\n\n';
        
        // معلومات عامة
        content += `تاريخ التوليد: ${new Date().toLocaleString('ar-EG')}\n`;
        content += `عدد الجولات: ${data.statistics.totalRounds}\n`;
        content += `إجمالي الرهانات: ${data.statistics.totalBets} جنيه\n\n`;
        
        // الجدول
        content += 'الجولة | النسبة | الرهان | الخسائر السابقة | الربح عند الفوز | الربح الصافي | الحالة\n';
        content += ''.padEnd(80, '-') + '\n';
        
        data.table.forEach(row => {
            const status = row.isWinning ? '✅' : '❌';
            content += `${row.round.toString().padStart(6)} | `;
            content += `${row.ratio.toString().padStart(6)} | `;
            content += `${row.bet.toString().padStart(6)} | `;
            content += `${row.previousLosses.toString().padStart(15)} | `;
            content += `${row.winAmount.toString().padStart(15)} | `;
            content += `${row.netProfit.toString().padStart(12)} | `;
            content += `${status}\n`;
        });
        
        // الإحصائيات
        content += '\n===== الإحصائيات =====\n';
        content += `إجمالي الجولات: ${data.statistics.totalRounds}\n`;
        content += `إجمالي الرهانات: ${data.statistics.totalBets} جنيه\n`;
        content += `متوسط الرهان: ${data.statistics.averageBet} جنيه\n`;
        content += `أعلى رهان: ${data.statistics.maxBet} جنيه\n`;
        content += `أقل رهان: ${data.statistics.minBet} جنيه\n`;
        content += `عدد الجولات الرابحة: ${data.statistics.winningRounds}\n`;
        content += `نسبة الفوز: ${data.statistics.winningPercentage.toFixed(2)}%\n`;
        content += `متوسط الربح: ${data.statistics.averageProfit} جنيه\n`;
        content += `أعلى ربح: ${data.statistics.maxProfit} جنيه\n`;
        content += `نسبة استخدام الرصيد: ${data.statistics.budgetUtilization}%\n`;
        content += `الرصيد المتبقي: ${data.statistics.remainingBudget} جنيه\n`;
        content += `مستوى المخاطرة: ${data.statistics.riskLevel}\n`;
        
        return content;
    }

    /**
     * إنشاء HTML كامل للتصدير
     */
    generateCompleteHTML(data) {
        return `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>جدول مراهنات الصاروخ - ${new Date().toLocaleDateString('ar-EG')}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; background: #f8f9fa; }
        .container { max-width: 1200px; margin: 2rem auto; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 2rem; border-radius: 10px; margin-bottom: 2rem; }
        .table { background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .table th { background: #495057; color: white; }
        .winning-row { background-color: #d4edda; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin: 2rem 0; }
        .stat-card { background: white; padding: 1.5rem; border-radius: 10px; text-align: center; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .search-box { margin-bottom: 1rem; }
        @media print { .no-print { display: none; } }
    </style>
</head>
<body>
    <div class="container">
        <div class="header text-center">
            <h1>🚀 جدول مراهنات الصاروخ</h1>
            <p>تم التوليد في: ${new Date().toLocaleString('ar-EG')}</p>
        </div>
        
        <div class="search-box no-print">
            <input type="text" class="form-control" id="searchInput" placeholder="البحث في الجدول..." onkeyup="searchTable()">
        </div>
        
        <div class="table-responsive">
            ${this.generateTableHTML(data)}
        </div>
        
        <div class="stats-grid">
            ${this.generateStatsHTML(data.statistics)}
        </div>
    </div>
    
    <script>
        function searchTable() {
            const input = document.getElementById('searchInput');
            const filter = input.value.toLowerCase();
            const table = document.querySelector('table');
            const rows = table.getElementsByTagName('tr');
            
            for (let i = 1; i < rows.length; i++) {
                const row = rows[i];
                const cells = row.getElementsByTagName('td');
                let found = false;
                
                for (let j = 0; j < cells.length; j++) {
                    if (cells[j].textContent.toLowerCase().includes(filter)) {
                        found = true;
                        break;
                    }
                }
                
                row.style.display = found ? '' : 'none';
            }
        }
    </script>
</body>
</html>`;
    }

    /**
     * إنشاء HTML للطباعة كـ PDF
     */
    generatePDFHTML(data) {
        return `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>جدول مراهنات الصاروخ</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
        .header { text-align: center; margin-bottom: 30px; }
        table { width: 100%; border-collapse: collapse; margin-bottom: 30px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
        th { background-color: #f2f2f2; font-weight: bold; }
        .winning-row { background-color: #d4edda; }
        .stats { margin-top: 30px; }
        .stat-item { margin-bottom: 10px; }
        @page { margin: 2cm; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 جدول مراهنات الصاروخ</h1>
        <p>تاريخ التوليد: ${new Date().toLocaleString('ar-EG')}</p>
    </div>
    
    ${this.generateTableHTML(data)}
    
    <div class="stats">
        <h3>الإحصائيات:</h3>
        ${this.generateStatsHTML(data.statistics)}
    </div>
</body>
</html>`;
    }

    /**
     * إنشاء HTML للجدول
     */
    generateTableHTML(data) {
        let html = `
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>الجولة</th>
                    <th>النسبة</th>
                    <th>الرهان (جنيه)</th>
                    <th>مجموع الخسائر السابقة</th>
                    <th>الربح عند الفوز</th>
                    <th>الربح الصافي</th>
                    <th>الحالة</th>
                </tr>
            </thead>
            <tbody>
        `;
        
        data.table.forEach(row => {
            const statusIcon = row.isWinning ? '✅' : '❌';
            const rowClass = row.isWinning ? 'winning-row' : '';
            const profitClass = row.netProfit > 0 ? 'text-success' : 'text-danger';
            
            html += `
                <tr class="${rowClass}">
                    <td>${row.round}</td>
                    <td>${row.ratio}</td>
                    <td>${row.bet}</td>
                    <td>${row.previousLosses}</td>
                    <td>${row.winAmount}</td>
                    <td class="${profitClass}">${row.netProfit > 0 ? '+' : ''}${row.netProfit}</td>
                    <td>${statusIcon}</td>
                </tr>
            `;
        });
        
        html += '</tbody></table>';
        return html;
    }

    /**
     * إنشاء HTML للإحصائيات
     */
    generateStatsHTML(stats) {
        return `
        <div class="stat-card">
            <h5>إجمالي الجولات</h5>
            <h3>${stats.totalRounds}</h3>
        </div>
        <div class="stat-card">
            <h5>إجمالي الرهانات</h5>
            <h3>${stats.totalBets} جنيه</h3>
        </div>
        <div class="stat-card">
            <h5>متوسط الرهان</h5>
            <h3>${stats.averageBet} جنيه</h3>
        </div>
        <div class="stat-card">
            <h5>نسبة الفوز</h5>
            <h3>${stats.winningPercentage.toFixed(1)}%</h3>
        </div>
        <div class="stat-card">
            <h5>مستوى المخاطرة</h5>
            <h3>${stats.riskLevel}</h3>
        </div>
        <div class="stat-card">
            <h5>الرصيد المتبقي</h5>
            <h3>${stats.remainingBudget} جنيه</h3>
        </div>
        `;
    }

    /**
     * تحميل الملف
     */
    downloadBlob(blob, filename) {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
    }

    /**
     * تسجيل عملية التصدير
     */
    recordExport(format, filename, data) {
        this.exportHistory.push({
            timestamp: new Date().toISOString(),
            format: format,
            filename: filename,
            recordCount: data.table ? data.table.length : 0
        });
    }

    /**
     * الحصول على تاريخ التصدير
     */
    getExportHistory() {
        return this.exportHistory;
    }

    /**
     * تصدير جميع الصيغ في ملف مضغوط
     */
    async exportAllFormats(data, baseFilename = 'rocket-betting-table') {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `${baseFilename}-${timestamp}`;
        
        // تصدير كل صيغة
        await this.exportToExcel(data, filename);
        await this.exportToPDF(data, filename);
        await this.exportToTXT(data, filename);
        await this.exportToHTML(data, filename);
        await this.exportToJSON(data, filename);
        
        return {
            success: true,
            message: 'تم تصدير جميع الصيغ بنجاح',
            formats: ['excel', 'pdf', 'txt', 'html', 'json']
        };
    }
}

// تصدير الكلاس للاستخدام العام
window.ExportSystem = ExportSystem;
