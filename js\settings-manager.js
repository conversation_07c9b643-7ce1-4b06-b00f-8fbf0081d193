/**
 * ===== مدير الإعدادات =====
 * نظام شامل لإدارة وحفظ واستيراد الإعدادات
 */

class SettingsManager {
    constructor() {
        this.defaultSettings = {
            totalBudget: 4000,
            targetRounds: 22,
            ratioIncrease: 5,
            startingRatio: 5,
            initialBet: 10,
            betIncrement: 10,
            aiMode: 'balanced',
            reinvestmentEnabled: true,
            reinvestmentStartRound: 15,
            reinvestmentPercentage: 50
        };
        
        this.currentSettings = { ...this.defaultSettings };
        this.settingsHistory = [];
        this.storageKey = 'rocketBettingSettings';
        
        // تحميل الإعدادات المحفوظة
        this.loadSettings();
    }

    /**
     * تحديث الإعدادات
     */
    updateSettings(newSettings) {
        // حفظ الإعدادات الحالية في التاريخ
        this.settingsHistory.push({
            timestamp: new Date().toISOString(),
            settings: { ...this.currentSettings }
        });
        
        // تحديث الإعدادات
        this.currentSettings = { ...this.currentSettings, ...newSettings };
        
        // حفظ في التخزين المحلي
        this.saveSettings();
        
        // التحقق من صحة الإعدادات
        return this.validateSettings();
    }

    /**
     * التحقق من صحة الإعدادات
     */
    validateSettings() {
        const errors = [];
        const settings = this.currentSettings;
        
        // التحقق من الرصيد الإجمالي
        if (!settings.totalBudget || settings.totalBudget < 100) {
            errors.push('الرصيد الإجمالي يجب أن يكون أكبر من 100 جنيه');
        }
        
        if (settings.totalBudget > 10000000) {
            errors.push('الرصيد الإجمالي كبير جداً');
        }
        
        // التحقق من عدد الجولات
        if (!settings.targetRounds || settings.targetRounds < 5) {
            errors.push('عدد الجولات يجب أن يكون أكبر من 5');
        }
        
        if (settings.targetRounds > 100) {
            errors.push('عدد الجولات كبير جداً (الحد الأقصى 100)');
        }
        
        // التحقق من زيادة النسبة
        if (!settings.ratioIncrease || settings.ratioIncrease < 1) {
            errors.push('زيادة النسبة يجب أن تكون أكبر من 1');
        }
        
        // التحقق من النسبة الأولى
        if (!settings.startingRatio || settings.startingRatio < 1.1) {
            errors.push('النسبة الأولى يجب أن تكون أكبر من 1.1');
        }
        
        if (settings.startingRatio > 50) {
            errors.push('النسبة الأولى كبيرة جداً');
        }
        
        // التحقق من الرهان الأولي
        if (!settings.initialBet || settings.initialBet < 1) {
            errors.push('الرهان الأولي يجب أن يكون أكبر من 1 جنيه');
        }
        
        if (settings.initialBet > settings.totalBudget * 0.5) {
            errors.push('الرهان الأولي كبير جداً مقارنة بالرصيد');
        }
        
        // التحقق من زيادة الرهان
        const allowedIncrements = [10, 20, 50, 100, 1000, 10000];
        if (!allowedIncrements.includes(settings.betIncrement)) {
            errors.push('زيادة الرهان يجب أن تكون من القيم المسموحة');
        }
        
        // التحقق من وضع الذكاء الصناعي
        const allowedModes = ['conservative', 'balanced', 'aggressive'];
        if (!allowedModes.includes(settings.aiMode)) {
            errors.push('وضع الذكاء الصناعي غير صحيح');
        }

        // التحقق من إعدادات إعادة الاستثمار
        if (settings.reinvestmentEnabled) {
            if (!settings.reinvestmentStartRound || settings.reinvestmentStartRound < 1) {
                errors.push('جولة بداية إعادة الاستثمار يجب أن تكون أكبر من 1');
            }

            if (settings.reinvestmentStartRound > settings.targetRounds) {
                errors.push('جولة بداية إعادة الاستثمار يجب أن تكون أقل من إجمالي الجولات');
            }

            if (!settings.reinvestmentPercentage || settings.reinvestmentPercentage < 10) {
                errors.push('نسبة إعادة الاستثمار يجب أن تكون أكبر من 10%');
            }

            if (settings.reinvestmentPercentage > 90) {
                errors.push('نسبة إعادة الاستثمار يجب أن تكون أقل من 90%');
            }
        }

        return {
            isValid: errors.length === 0,
            errors: errors,
            warnings: this.generateWarnings()
        };
    }

    /**
     * توليد التحذيرات
     */
    generateWarnings() {
        const warnings = [];
        const settings = this.currentSettings;
        
        // تحذيرات المخاطرة
        if (settings.initialBet > settings.totalBudget * 0.1) {
            warnings.push('الرهان الأولي مرتفع نسبياً - قد يزيد المخاطرة');
        }
        
        if (settings.targetRounds > 30) {
            warnings.push('عدد الجولات كبير - قد يتطلب رصيد أكبر');
        }
        
        if (settings.betIncrement >= 1000) {
            warnings.push('زيادة الرهان كبيرة - قد تؤدي لاستنزاف الرصيد بسرعة');
        }
        
        if (settings.startingRatio < 3) {
            warnings.push('النسبة الأولى منخفضة - قد تقلل الأرباح');
        }
        
        if (settings.aiMode === 'aggressive') {
            warnings.push('الوضع العدواني يزيد المخاطرة - استخدم بحذر');
        }

        // تحذيرات إعادة الاستثمار
        if (settings.reinvestmentEnabled) {
            if (settings.reinvestmentPercentage > 70) {
                warnings.push('نسبة إعادة الاستثمار عالية - قد تزيد المخاطرة');
            }

            if (settings.reinvestmentStartRound < 10) {
                warnings.push('بداية إعادة الاستثمار مبكرة - تأكد من وجود أرباح كافية');
            }

            if (settings.reinvestmentStartRound > settings.targetRounds * 0.8) {
                warnings.push('بداية إعادة الاستثمار متأخرة - قد لا تحقق فائدة كبيرة');
            }
        }

        return warnings;
    }

    /**
     * حفظ الإعدادات في التخزين المحلي
     */
    saveSettings() {
        try {
            const dataToSave = {
                settings: this.currentSettings,
                timestamp: new Date().toISOString(),
                version: '1.0'
            };
            
            localStorage.setItem(this.storageKey, JSON.stringify(dataToSave));
            return true;
        } catch (error) {
            console.error('خطأ في حفظ الإعدادات:', error);
            return false;
        }
    }

    /**
     * تحميل الإعدادات من التخزين المحلي
     */
    loadSettings() {
        try {
            const savedData = localStorage.getItem(this.storageKey);
            if (savedData) {
                const parsedData = JSON.parse(savedData);
                if (parsedData.settings) {
                    this.currentSettings = { ...this.defaultSettings, ...parsedData.settings };
                }
            }
            return true;
        } catch (error) {
            console.error('خطأ في تحميل الإعدادات:', error);
            this.currentSettings = { ...this.defaultSettings };
            return false;
        }
    }

    /**
     * تصدير الإعدادات إلى ملف
     */
    exportSettings() {
        const exportData = {
            settings: this.currentSettings,
            metadata: {
                exportDate: new Date().toISOString(),
                version: '1.0',
                appName: 'أداة مراهنات الصاروخ',
                description: 'ملف إعدادات مصدر من أداة مراهنات الصاروخ'
            },
            validation: this.validateSettings()
        };
        
        const jsonString = JSON.stringify(exportData, null, 2);
        const blob = new Blob([jsonString], { type: 'application/json' });
        
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `rocket-betting-settings-${timestamp}.json`;
        
        this.downloadBlob(blob, filename);
        
        return {
            success: true,
            filename: filename,
            size: blob.size
        };
    }

    /**
     * استيراد الإعدادات من ملف
     */
    async importSettings(file) {
        return new Promise((resolve, reject) => {
            if (!file) {
                reject(new Error('لم يتم تحديد ملف'));
                return;
            }
            
            if (!file.name.endsWith('.json')) {
                reject(new Error('يجب أن يكون الملف من نوع JSON'));
                return;
            }
            
            const reader = new FileReader();
            
            reader.onload = (e) => {
                try {
                    const importedData = JSON.parse(e.target.result);
                    
                    // التحقق من صحة البيانات المستوردة
                    if (!importedData.settings) {
                        throw new Error('ملف الإعدادات غير صحيح - لا يحتوي على إعدادات');
                    }
                    
                    // التحقق من الإصدار
                    if (importedData.metadata && importedData.metadata.version !== '1.0') {
                        console.warn('إصدار مختلف من ملف الإعدادات');
                    }
                    
                    // تحديث الإعدادات
                    const validationResult = this.updateSettings(importedData.settings);
                    
                    if (validationResult.isValid) {
                        resolve({
                            success: true,
                            message: 'تم استيراد الإعدادات بنجاح',
                            settings: this.currentSettings,
                            warnings: validationResult.warnings
                        });
                    } else {
                        reject(new Error('الإعدادات المستوردة غير صحيحة: ' + validationResult.errors.join(', ')));
                    }
                    
                } catch (error) {
                    reject(new Error('خطأ في قراءة ملف الإعدادات: ' + error.message));
                }
            };
            
            reader.onerror = () => {
                reject(new Error('خطأ في قراءة الملف'));
            };
            
            reader.readAsText(file);
        });
    }

    /**
     * إعادة تعيين الإعدادات للقيم الافتراضية
     */
    resetToDefaults() {
        this.settingsHistory.push({
            timestamp: new Date().toISOString(),
            settings: { ...this.currentSettings },
            action: 'reset_to_defaults'
        });
        
        this.currentSettings = { ...this.defaultSettings };
        this.saveSettings();
        
        return this.currentSettings;
    }

    /**
     * الحصول على الإعدادات الحالية
     */
    getCurrentSettings() {
        return { ...this.currentSettings };
    }

    /**
     * الحصول على تاريخ الإعدادات
     */
    getSettingsHistory() {
        return [...this.settingsHistory];
    }

    /**
     * استرجاع إعدادات سابقة
     */
    restorePreviousSettings(index) {
        if (index >= 0 && index < this.settingsHistory.length) {
            const previousSettings = this.settingsHistory[index].settings;
            this.updateSettings(previousSettings);
            return true;
        }
        return false;
    }

    /**
     * إنشاء ملف تعريف للإعدادات
     */
    createSettingsProfile(name, description = '') {
        const profile = {
            name: name,
            description: description,
            settings: { ...this.currentSettings },
            createdAt: new Date().toISOString(),
            id: this.generateId()
        };
        
        // حفظ الملف التعريفي
        const profiles = this.getSavedProfiles();
        profiles.push(profile);
        localStorage.setItem('rocketBettingProfiles', JSON.stringify(profiles));
        
        return profile;
    }

    /**
     * الحصول على الملفات التعريفية المحفوظة
     */
    getSavedProfiles() {
        try {
            const profiles = localStorage.getItem('rocketBettingProfiles');
            return profiles ? JSON.parse(profiles) : [];
        } catch (error) {
            console.error('خطأ في تحميل الملفات التعريفية:', error);
            return [];
        }
    }

    /**
     * تحميل ملف تعريفي
     */
    loadProfile(profileId) {
        const profiles = this.getSavedProfiles();
        const profile = profiles.find(p => p.id === profileId);
        
        if (profile) {
            this.updateSettings(profile.settings);
            return true;
        }
        return false;
    }

    /**
     * حذف ملف تعريفي
     */
    deleteProfile(profileId) {
        const profiles = this.getSavedProfiles();
        const filteredProfiles = profiles.filter(p => p.id !== profileId);
        localStorage.setItem('rocketBettingProfiles', JSON.stringify(filteredProfiles));
        return true;
    }

    /**
     * توليد معرف فريد
     */
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    /**
     * تحميل ملف
     */
    downloadBlob(blob, filename) {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
    }

    /**
     * الحصول على ملخص الإعدادات
     */
    getSettingsSummary() {
        const settings = this.currentSettings;
        const validation = this.validateSettings();
        
        return {
            settings: settings,
            validation: validation,
            estimatedBudgetUsage: this.estimateBudgetUsage(),
            riskLevel: this.calculateRiskLevel(),
            recommendations: this.getRecommendations()
        };
    }

    /**
     * تقدير استخدام الرصيد
     */
    estimateBudgetUsage() {
        const settings = this.currentSettings;
        let totalBets = 0;
        
        for (let round = 1; round <= settings.targetRounds; round++) {
            const bet = settings.initialBet + ((round - 1) * settings.betIncrement);
            totalBets += bet;
            
            if (totalBets > settings.totalBudget) {
                return {
                    estimatedUsage: totalBets,
                    percentage: (totalBets / settings.totalBudget) * 100,
                    exceedsLimit: true,
                    maxRounds: round - 1
                };
            }
        }
        
        return {
            estimatedUsage: totalBets,
            percentage: (totalBets / settings.totalBudget) * 100,
            exceedsLimit: false,
            maxRounds: settings.targetRounds
        };
    }

    /**
     * حساب مستوى المخاطرة
     */
    calculateRiskLevel() {
        const budgetUsage = this.estimateBudgetUsage();
        
        if (budgetUsage.exceedsLimit) return 'خطر جداً';
        if (budgetUsage.percentage > 90) return 'عالي جداً';
        if (budgetUsage.percentage > 75) return 'عالي';
        if (budgetUsage.percentage > 50) return 'متوسط';
        if (budgetUsage.percentage > 25) return 'منخفض';
        return 'آمن';
    }

    /**
     * الحصول على التوصيات
     */
    getRecommendations() {
        const recommendations = [];
        const settings = this.currentSettings;
        const budgetUsage = this.estimateBudgetUsage();
        
        if (budgetUsage.exceedsLimit) {
            recommendations.push('قلل عدد الجولات أو زيادة الرهان لتجنب تجاوز الرصيد');
        }
        
        if (settings.initialBet > settings.totalBudget * 0.05) {
            recommendations.push('الرهان الأولي مرتفع - فكر في تقليله');
        }
        
        if (settings.betIncrement >= 1000 && settings.targetRounds > 15) {
            recommendations.push('زيادة الرهان كبيرة مع عدد جولات كثير - قد تحتاج رصيد أكبر');
        }
        
        if (settings.startingRatio < 3) {
            recommendations.push('النسبة الأولى منخفضة - فكر في زيادتها لأرباح أفضل');
        }
        
        return recommendations;
    }
}

// تصدير الكلاس للاستخدام العام
window.SettingsManager = SettingsManager;
