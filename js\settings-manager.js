/**
 * ===== مدير الإعدادات =====
 * نظام شامل لإدارة وحفظ واستيراد الإعدادات
 */

class SettingsManager {
    constructor() {
        this.defaultSettings = {
            totalBudget: 4000,
            targetRounds: 22,
            ratioIncrease: 5,
            startingRatio: 5,
            initialBet: 10,
            betIncrement: 10,
            aiMode: 'balanced',
            // الإعدادات المتقدمة الجديدة
            customPrompt: '',
            autoAdjustment: false,
            adjustmentThreshold: 10,
            exitStrategy: false,
            exitAfterRounds: 20,
            maxLossThreshold: 2000,
            exitProfitReduction: 20,
            exitRounds: 8
        };
        
        this.currentSettings = { ...this.defaultSettings };
        this.settingsHistory = [];
        this.storageKey = 'rocketBettingSettings';
        
        // تحميل الإعدادات المحفوظة
        this.loadSettings();
    }

    /**
     * تحديث الإعدادات
     */
    updateSettings(newSettings) {
        // حفظ الإعدادات الحالية في التاريخ
        this.settingsHistory.push({
            timestamp: new Date().toISOString(),
            settings: { ...this.currentSettings }
        });
        
        // تحديث الإعدادات
        this.currentSettings = { ...this.currentSettings, ...newSettings };
        
        // حفظ في التخزين المحلي
        this.saveSettings();
        
        // التحقق من صحة الإعدادات
        return this.validateSettings();
    }

    /**
     * التحقق من صحة الإعدادات
     */
    validateSettings() {
        const errors = [];
        const settings = this.currentSettings;
        
        // التحقق من الرصيد الإجمالي
        if (!settings.totalBudget || settings.totalBudget < 100) {
            errors.push('الرصيد الإجمالي يجب أن يكون أكبر من 100 جنيه');
        }
        
        if (settings.totalBudget > 10000000) {
            errors.push('الرصيد الإجمالي كبير جداً');
        }
        
        // التحقق من عدد الجولات
        if (!settings.targetRounds || settings.targetRounds < 5) {
            errors.push('عدد الجولات يجب أن يكون أكبر من 5');
        }
        
        if (settings.targetRounds > 100) {
            errors.push('عدد الجولات كبير جداً (الحد الأقصى 100)');
        }
        
        // التحقق من زيادة النسبة
        if (!settings.ratioIncrease || settings.ratioIncrease < 1) {
            errors.push('زيادة النسبة يجب أن تكون أكبر من 1');
        }
        
        // التحقق من النسبة الأولى
        if (!settings.startingRatio || settings.startingRatio < 1.1) {
            errors.push('النسبة الأولى يجب أن تكون أكبر من 1.1');
        }
        
        if (settings.startingRatio > 50) {
            errors.push('النسبة الأولى كبيرة جداً');
        }
        
        // التحقق من الرهان الأولي
        if (!settings.initialBet || settings.initialBet < 1) {
            errors.push('الرهان الأولي يجب أن يكون أكبر من 1 جنيه');
        }
        
        if (settings.initialBet > settings.totalBudget * 0.5) {
            errors.push('الرهان الأولي كبير جداً مقارنة بالرصيد');
        }
        
        // التحقق من زيادة الرهان
        const allowedIncrements = [10, 20, 50, 100, 1000, 10000];
        if (!allowedIncrements.includes(settings.betIncrement)) {
            errors.push('زيادة الرهان يجب أن تكون من القيم المسموحة');
        }
        
        // التحقق من وضع الذكاء الصناعي
        const allowedModes = ['conservative', 'balanced', 'aggressive'];
        if (!allowedModes.includes(settings.aiMode)) {
            errors.push('وضع الذكاء الصناعي غير صحيح');
        }

        // التحقق من الإعدادات المتقدمة
        if (settings.customPrompt && settings.customPrompt.length > 500) {
            errors.push('التعليمات الإضافية طويلة جداً (الحد الأقصى 500 حرف)');
        }

        if (settings.adjustmentThreshold && (settings.adjustmentThreshold < 3 || settings.adjustmentThreshold > 20)) {
            errors.push('عدد الجولات للتعديل يجب أن يكون بين 3 و 20');
        }

        if (settings.exitAfterRounds && (settings.exitAfterRounds < 5 || settings.exitAfterRounds > 50)) {
            errors.push('عدد جولات الانسحاب يجب أن يكون بين 5 و 50');
        }

        if (settings.maxLossThreshold && settings.maxLossThreshold > settings.totalBudget) {
            errors.push('حد الخسارة القصوى لا يمكن أن يكون أكبر من الرصيد الإجمالي');
        }

        if (settings.exitProfitReduction && (settings.exitProfitReduction < 5 || settings.exitProfitReduction > 50)) {
            errors.push('تقليل نسبة الربح يجب أن يكون بين 5% و 50%');
        }

        return {
            isValid: errors.length === 0,
            errors: errors,
            warnings: this.generateWarnings()
        };
    }

    /**
     * توليد التحذيرات
     */
    generateWarnings() {
        const warnings = [];
        const settings = this.currentSettings;
        
        // تحذيرات المخاطرة
        if (settings.initialBet > settings.totalBudget * 0.1) {
            warnings.push('الرهان الأولي مرتفع نسبياً - قد يزيد المخاطرة');
        }
        
        if (settings.targetRounds > 30) {
            warnings.push('عدد الجولات كبير - قد يتطلب رصيد أكبر');
        }
        
        if (settings.betIncrement >= 1000) {
            warnings.push('زيادة الرهان كبيرة - قد تؤدي لاستنزاف الرصيد بسرعة');
        }
        
        if (settings.startingRatio < 3) {
            warnings.push('النسبة الأولى منخفضة - قد تقلل الأرباح');
        }
        
        if (settings.aiMode === 'aggressive') {
            warnings.push('الوضع العدواني يزيد المخاطرة - استخدم بحذر');
        }
        
        return warnings;
    }

    /**
     * حفظ الإعدادات في التخزين المحلي
     */
    saveSettings() {
        try {
            const dataToSave = {
                settings: this.currentSettings,
                timestamp: new Date().toISOString(),
                version: '1.0'
            };
            
            localStorage.setItem(this.storageKey, JSON.stringify(dataToSave));
            return true;
        } catch (error) {
            console.error('خطأ في حفظ الإعدادات:', error);
            return false;
        }
    }

    /**
     * تحميل الإعدادات من التخزين المحلي
     */
    loadSettings() {
        try {
            const savedData = localStorage.getItem(this.storageKey);
            if (savedData) {
                const parsedData = JSON.parse(savedData);
                if (parsedData.settings) {
                    this.currentSettings = { ...this.defaultSettings, ...parsedData.settings };
                }
            }
            return true;
        } catch (error) {
            console.error('خطأ في تحميل الإعدادات:', error);
            this.currentSettings = { ...this.defaultSettings };
            return false;
        }
    }

    /**
     * تصدير الإعدادات إلى ملف
     */
    exportSettings() {
        const exportData = {
            settings: this.currentSettings,
            metadata: {
                exportDate: new Date().toISOString(),
                version: '1.0',
                appName: 'أداة مراهنات الصاروخ',
                description: 'ملف إعدادات مصدر من أداة مراهنات الصاروخ'
            },
            validation: this.validateSettings()
        };
        
        const jsonString = JSON.stringify(exportData, null, 2);
        const blob = new Blob([jsonString], { type: 'application/json' });
        
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `rocket-betting-settings-${timestamp}.json`;
        
        this.downloadBlob(blob, filename);
        
        return {
            success: true,
            filename: filename,
            size: blob.size
        };
    }

    /**
     * استيراد الإعدادات من ملف
     */
    async importSettings(file) {
        return new Promise((resolve, reject) => {
            if (!file) {
                reject(new Error('لم يتم تحديد ملف'));
                return;
            }
            
            if (!file.name.endsWith('.json')) {
                reject(new Error('يجب أن يكون الملف من نوع JSON'));
                return;
            }
            
            const reader = new FileReader();
            
            reader.onload = (e) => {
                try {
                    const importedData = JSON.parse(e.target.result);
                    
                    // التحقق من صحة البيانات المستوردة
                    if (!importedData.settings) {
                        throw new Error('ملف الإعدادات غير صحيح - لا يحتوي على إعدادات');
                    }
                    
                    // التحقق من الإصدار
                    if (importedData.metadata && importedData.metadata.version !== '1.0') {
                        console.warn('إصدار مختلف من ملف الإعدادات');
                    }
                    
                    // تحديث الإعدادات
                    const validationResult = this.updateSettings(importedData.settings);
                    
                    if (validationResult.isValid) {
                        resolve({
                            success: true,
                            message: 'تم استيراد الإعدادات بنجاح',
                            settings: this.currentSettings,
                            warnings: validationResult.warnings
                        });
                    } else {
                        reject(new Error('الإعدادات المستوردة غير صحيحة: ' + validationResult.errors.join(', ')));
                    }
                    
                } catch (error) {
                    reject(new Error('خطأ في قراءة ملف الإعدادات: ' + error.message));
                }
            };
            
            reader.onerror = () => {
                reject(new Error('خطأ في قراءة الملف'));
            };
            
            reader.readAsText(file);
        });
    }

    /**
     * إعادة تعيين الإعدادات للقيم الافتراضية
     */
    resetToDefaults() {
        this.settingsHistory.push({
            timestamp: new Date().toISOString(),
            settings: { ...this.currentSettings },
            action: 'reset_to_defaults'
        });
        
        this.currentSettings = { ...this.defaultSettings };
        this.saveSettings();
        
        return this.currentSettings;
    }

    /**
     * الحصول على الإعدادات الحالية
     */
    getCurrentSettings() {
        return { ...this.currentSettings };
    }

    /**
     * الحصول على تاريخ الإعدادات
     */
    getSettingsHistory() {
        return [...this.settingsHistory];
    }

    /**
     * استرجاع إعدادات سابقة
     */
    restorePreviousSettings(index) {
        if (index >= 0 && index < this.settingsHistory.length) {
            const previousSettings = this.settingsHistory[index].settings;
            this.updateSettings(previousSettings);
            return true;
        }
        return false;
    }

    /**
     * إنشاء ملف تعريف للإعدادات
     */
    createSettingsProfile(name, description = '') {
        const profile = {
            name: name,
            description: description,
            settings: { ...this.currentSettings },
            createdAt: new Date().toISOString(),
            id: this.generateId()
        };
        
        // حفظ الملف التعريفي
        const profiles = this.getSavedProfiles();
        profiles.push(profile);
        localStorage.setItem('rocketBettingProfiles', JSON.stringify(profiles));
        
        return profile;
    }

    /**
     * الحصول على الملفات التعريفية المحفوظة
     */
    getSavedProfiles() {
        try {
            const profiles = localStorage.getItem('rocketBettingProfiles');
            return profiles ? JSON.parse(profiles) : [];
        } catch (error) {
            console.error('خطأ في تحميل الملفات التعريفية:', error);
            return [];
        }
    }

    /**
     * تحميل ملف تعريفي
     */
    loadProfile(profileId) {
        const profiles = this.getSavedProfiles();
        const profile = profiles.find(p => p.id === profileId);
        
        if (profile) {
            this.updateSettings(profile.settings);
            return true;
        }
        return false;
    }

    /**
     * حذف ملف تعريفي
     */
    deleteProfile(profileId) {
        const profiles = this.getSavedProfiles();
        const filteredProfiles = profiles.filter(p => p.id !== profileId);
        localStorage.setItem('rocketBettingProfiles', JSON.stringify(filteredProfiles));
        return true;
    }

    /**
     * توليد معرف فريد
     */
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    /**
     * تحميل ملف
     */
    downloadBlob(blob, filename) {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
    }

    /**
     * الحصول على ملخص الإعدادات
     */
    getSettingsSummary() {
        const settings = this.currentSettings;
        const validation = this.validateSettings();
        
        return {
            settings: settings,
            validation: validation,
            estimatedBudgetUsage: this.estimateBudgetUsage(),
            riskLevel: this.calculateRiskLevel(),
            recommendations: this.getRecommendations()
        };
    }

    /**
     * تقدير استخدام الرصيد
     */
    estimateBudgetUsage() {
        const settings = this.currentSettings;
        let totalBets = 0;
        
        for (let round = 1; round <= settings.targetRounds; round++) {
            const bet = settings.initialBet + ((round - 1) * settings.betIncrement);
            totalBets += bet;
            
            if (totalBets > settings.totalBudget) {
                return {
                    estimatedUsage: totalBets,
                    percentage: (totalBets / settings.totalBudget) * 100,
                    exceedsLimit: true,
                    maxRounds: round - 1
                };
            }
        }
        
        return {
            estimatedUsage: totalBets,
            percentage: (totalBets / settings.totalBudget) * 100,
            exceedsLimit: false,
            maxRounds: settings.targetRounds
        };
    }

    /**
     * حساب مستوى المخاطرة
     */
    calculateRiskLevel() {
        const budgetUsage = this.estimateBudgetUsage();
        
        if (budgetUsage.exceedsLimit) return 'خطر جداً';
        if (budgetUsage.percentage > 90) return 'عالي جداً';
        if (budgetUsage.percentage > 75) return 'عالي';
        if (budgetUsage.percentage > 50) return 'متوسط';
        if (budgetUsage.percentage > 25) return 'منخفض';
        return 'آمن';
    }

    /**
     * الحصول على التوصيات
     */
    getRecommendations() {
        const recommendations = [];
        const settings = this.currentSettings;
        const budgetUsage = this.estimateBudgetUsage();
        
        if (budgetUsage.exceedsLimit) {
            recommendations.push('قلل عدد الجولات أو زيادة الرهان لتجنب تجاوز الرصيد');
        }
        
        if (settings.initialBet > settings.totalBudget * 0.05) {
            recommendations.push('الرهان الأولي مرتفع - فكر في تقليله');
        }
        
        if (settings.betIncrement >= 1000 && settings.targetRounds > 15) {
            recommendations.push('زيادة الرهان كبيرة مع عدد جولات كثير - قد تحتاج رصيد أكبر');
        }
        
        if (settings.startingRatio < 3) {
            recommendations.push('النسبة الأولى منخفضة - فكر في زيادتها لأرباح أفضل');
        }
        
        return recommendations;
    }

    /**
     * تحديث حقول النموذج بالإعدادات الحالية
     */
    updateFormFields() {
        const settings = this.currentSettings;

        // الحقول الأساسية
        document.getElementById('totalBudget').value = settings.totalBudget;
        document.getElementById('targetRounds').value = settings.targetRounds;
        document.getElementById('ratioIncrease').value = settings.ratioIncrease;
        document.getElementById('startingRatio').value = settings.startingRatio;
        document.getElementById('initialBet').value = settings.initialBet;
        document.getElementById('betIncrement').value = settings.betIncrement;
        document.getElementById('aiMode').value = settings.aiMode;

        // الحقول المتقدمة الجديدة
        if (document.getElementById('customPrompt')) {
            document.getElementById('customPrompt').value = settings.customPrompt || '';
        }

        if (document.getElementById('autoAdjustment')) {
            document.getElementById('autoAdjustment').checked = settings.autoAdjustment || false;
        }

        if (document.getElementById('adjustmentThreshold')) {
            document.getElementById('adjustmentThreshold').value = settings.adjustmentThreshold || 10;
        }

        if (document.getElementById('exitStrategy')) {
            document.getElementById('exitStrategy').checked = settings.exitStrategy || false;
            // إظهار/إخفاء تفاصيل خطة الانسحاب
            const details = document.getElementById('exitStrategyDetails');
            if (details) {
                details.style.display = settings.exitStrategy ? 'block' : 'none';
            }
        }

        if (document.getElementById('exitAfterRounds')) {
            document.getElementById('exitAfterRounds').value = settings.exitAfterRounds || 20;
        }

        if (document.getElementById('maxLossThreshold')) {
            document.getElementById('maxLossThreshold').value = settings.maxLossThreshold || 2000;
        }

        if (document.getElementById('exitProfitReduction')) {
            document.getElementById('exitProfitReduction').value = settings.exitProfitReduction || 20;
        }

        if (document.getElementById('exitRounds')) {
            document.getElementById('exitRounds').value = settings.exitRounds || 8;
        }
    }

    /**
     * جمع الإعدادات من حقول النموذج
     */
    collectFormSettings() {
        const settings = {
            // الحقول الأساسية
            totalBudget: parseInt(document.getElementById('totalBudget').value),
            targetRounds: parseInt(document.getElementById('targetRounds').value),
            ratioIncrease: parseFloat(document.getElementById('ratioIncrease').value),
            startingRatio: parseFloat(document.getElementById('startingRatio').value),
            initialBet: parseInt(document.getElementById('initialBet').value),
            betIncrement: parseInt(document.getElementById('betIncrement').value),
            aiMode: document.getElementById('aiMode').value
        };

        // الحقول المتقدمة الجديدة
        if (document.getElementById('customPrompt')) {
            settings.customPrompt = document.getElementById('customPrompt').value.trim();
        }

        if (document.getElementById('autoAdjustment')) {
            settings.autoAdjustment = document.getElementById('autoAdjustment').checked;
        }

        if (document.getElementById('adjustmentThreshold')) {
            settings.adjustmentThreshold = parseInt(document.getElementById('adjustmentThreshold').value);
        }

        if (document.getElementById('exitStrategy')) {
            settings.exitStrategy = document.getElementById('exitStrategy').checked;
        }

        if (document.getElementById('exitAfterRounds')) {
            settings.exitAfterRounds = parseInt(document.getElementById('exitAfterRounds').value);
        }

        if (document.getElementById('maxLossThreshold')) {
            settings.maxLossThreshold = parseInt(document.getElementById('maxLossThreshold').value);
        }

        if (document.getElementById('exitProfitReduction')) {
            settings.exitProfitReduction = parseInt(document.getElementById('exitProfitReduction').value);
        }

        if (document.getElementById('exitRounds')) {
            settings.exitRounds = parseInt(document.getElementById('exitRounds').value);
        }

        return settings;
    }
}

// تصدير الكلاس للاستخدام العام
window.SettingsManager = SettingsManager;
