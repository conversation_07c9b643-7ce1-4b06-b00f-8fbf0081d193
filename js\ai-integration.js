/**
 * ===== تكامل الذكاء الصناعي - Gemini AI =====
 * نظام متقدم لإدارة واجهات برمجة التطبيقات مع تدوير المفاتيح
 */

class GeminiAIIntegration {
    constructor() {
        // مفاتيح Gemini AI مع نظام التدوير
        this.apiKeys = [
            'AIzaSyD4ouec6Q4EN1TbSi7LnQfKl9cqQEHegzQ',
            'AIzaSyDEdwpwXRAydelTq0BXHAfE3-tSlMga3xg',
            'AIzaSyCH5jU2QrczZOLPqNsceh_um6qs_N3Skgw',
            'AIzaSyCTYMFClrP1A1by2rTXTRYjl3mi-FfVvak',
            'AIzaSyDzSRdhUClIXUWpi9QRKi0hfB29_bzaQAc',
            'AIzaSyBzlVUgjCkG44mm3_8Rt-ZiLskXsxM29SM',
            'AIzaSyDeSqeSAb4QZ6JEf_LbLTrnVOgcGMSb7ds',
            'AIzaSyAu5ToET8qHvDUPzrDOvkr1T99qZXvZORk',
            'AIzaSyAFu5lHg2W5ibpFbm-k3kNlW-rI96JcQOg',
            'AIzaSyBThniUP9oFxyriMOV4-LpvmJtW3u1gZCQ',
            'AIzaSyAns8tVQ-V6sqbh7UPQB7tqYgWAZUIN6Z4',
            'AIzaSyCS32gY-wcVTRIuE2PY_3ppVloGmvFlAqo',
            'AIzaSyCc3Y4hHWuAxKtJx4E0h-spwYUP6Eqzk4k',
            'AIzaSyAmxg1f8A3s1mXxdUav0zeyC52BF-5QBoA',
            'AIzaSyD7C9Ry7EeCXwvrLkymXuUp3FwqbqloFuQ'
        ];
        
        this.currentKeyIndex = 0;
        this.requestCounts = new Array(this.apiKeys.length).fill(0);
        this.lastRequestTime = new Array(this.apiKeys.length).fill(0);
        this.maxRequestsPerMinute = 60;
        this.maxTokensPerRequest = 30000;
        
        this.baseUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent';
    }

    /**
     * الحصول على المفتاح التالي المتاح
     */
    getNextAvailableKey() {
        const now = Date.now();
        
        for (let i = 0; i < this.apiKeys.length; i++) {
            const keyIndex = (this.currentKeyIndex + i) % this.apiKeys.length;
            const timeSinceLastRequest = now - this.lastRequestTime[keyIndex];
            
            // إعادة تعيين العداد كل دقيقة
            if (timeSinceLastRequest > 60000) {
                this.requestCounts[keyIndex] = 0;
            }
            
            // التحقق من توفر المفتاح
            if (this.requestCounts[keyIndex] < this.maxRequestsPerMinute) {
                this.currentKeyIndex = keyIndex;
                return this.apiKeys[keyIndex];
            }
        }
        
        throw new Error('جميع مفاتيح API وصلت للحد الأقصى. يرجى المحاولة لاحقاً.');
    }

    /**
     * تسجيل استخدام المفتاح
     */
    recordKeyUsage(keyIndex) {
        this.requestCounts[keyIndex]++;
        this.lastRequestTime[keyIndex] = Date.now();
    }

    /**
     * تقسيم النص الطويل إلى أجزاء
     */
    splitLongText(text, maxLength = 25000) {
        if (text.length <= maxLength) {
            return [text];
        }
        
        const chunks = [];
        let currentChunk = '';
        const sentences = text.split('. ');
        
        for (const sentence of sentences) {
            if ((currentChunk + sentence).length > maxLength) {
                if (currentChunk) {
                    chunks.push(currentChunk.trim());
                    currentChunk = sentence + '. ';
                } else {
                    // الجملة طويلة جداً، قسمها بالقوة
                    chunks.push(sentence.substring(0, maxLength));
                    currentChunk = sentence.substring(maxLength) + '. ';
                }
            } else {
                currentChunk += sentence + '. ';
            }
        }
        
        if (currentChunk.trim()) {
            chunks.push(currentChunk.trim());
        }
        
        return chunks;
    }

    /**
     * إرسال طلب إلى Gemini AI
     */
    async sendRequest(prompt, retries = 3) {
        for (let attempt = 0; attempt < retries; attempt++) {
            try {
                const apiKey = this.getNextAvailableKey();
                const keyIndex = this.currentKeyIndex;
                
                const response = await fetch(`${this.baseUrl}?key=${apiKey}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        contents: [{
                            parts: [{ text: prompt }]
                        }],
                        generationConfig: {
                            temperature: 0.7,
                            topK: 40,
                            topP: 0.95,
                            maxOutputTokens: 8192,
                        }
                    })
                });

                this.recordKeyUsage(keyIndex);

                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(`HTTP ${response.status}: ${errorData.error?.message || 'خطأ غير معروف'}`);
                }

                const data = await response.json();
                
                if (data.candidates && data.candidates[0] && data.candidates[0].content) {
                    return data.candidates[0].content.parts[0].text;
                } else {
                    throw new Error('استجابة غير صالحة من الذكاء الصناعي');
                }
                
            } catch (error) {
                console.error(`محاولة ${attempt + 1} فشلت:`, error.message);
                
                if (attempt === retries - 1) {
                    throw error;
                }
                
                // انتظار قبل المحاولة التالية
                await this.delay(1000 * (attempt + 1));
            }
        }
    }

    /**
     * معالجة النصوص الطويلة بالتقسيم
     */
    async processLongText(text, promptTemplate, progressCallback) {
        const chunks = this.splitLongText(text);
        const results = [];
        
        for (let i = 0; i < chunks.length; i++) {
            const chunk = chunks[i];
            const prompt = promptTemplate.replace('{text}', chunk);
            
            try {
                const result = await this.sendRequest(prompt);
                results.push(result);
                
                if (progressCallback) {
                    progressCallback((i + 1) / chunks.length * 100);
                }
                
                // انتظار قصير بين الطلبات
                await this.delay(500);
                
            } catch (error) {
                console.error(`خطأ في معالجة الجزء ${i + 1}:`, error.message);
                results.push(`خطأ في معالجة الجزء ${i + 1}: ${error.message}`);
            }
        }
        
        return results.join('\n\n');
    }

    /**
     * توليد جدول مراهنات محسن بالذكاء الصناعي
     */
    async generateOptimizedTable(settings, progressCallback) {
        let prompt = `
أنت خبير في استراتيجيات المراهنة والحسابات المالية. مطلوب منك تحليل وتحسين جدول مراهنات الصاروخ بناءً على المعطيات التالية:

الإعدادات:
- الرصيد الإجمالي: ${settings.totalBudget} جنيه
- عدد الجولات المستهدف: ${settings.targetRounds}
- زيادة النسبة كل: ${settings.ratioIncrease} جولات
- النسبة الأولى: ${settings.startingRatio}
- الرهان الأولي: ${settings.initialBet} جنيه
- زيادة الرهان: ${settings.betIncrement} جنيه
- وضع الذكاء الصناعي: ${settings.aiMode}`;

        // إضافة معلومات الإعدادات المتقدمة
        if (settings.autoAdjustment) {
            prompt += `\n- التعديل التلقائي: مفعل (عتبة: ${settings.adjustmentThreshold} جولات)`;
        }

        if (settings.exitStrategy) {
            prompt += `\n- خطة الانسحاب: مفعلة (بعد ${settings.exitAfterRounds} جولة أو ${settings.maxLossThreshold} جنيه خسارة)`;
        }

        // إضافة التعليمات المخصصة إذا كانت موجودة
        if (settings.customPrompt && settings.customPrompt.trim()) {
            prompt += `\n\nتعليمات إضافية من المستخدم:
${settings.customPrompt.trim()}`;
        }

        prompt += `

المطلوب:
1. تحليل الإعدادات وتقييم مدى واقعيتها
2. اقتراح تحسينات على توزيع الرهانات
3. حساب احتمالية النجاح والمخاطر
4. تقديم توصيات لتحسين الاستراتيجية`;

        if (settings.autoAdjustment) {
            prompt += `\n5. تقييم فعالية التعديل التلقائي`;
        }

        if (settings.exitStrategy) {
            prompt += `\n6. تحليل خطة الانسحاب المقترحة`;
        }

        prompt += `\n\nيرجى تقديم تحليل مفصل ومقترحات عملية لتحسين الجدول.`;

        // إضافة تنسيق HTML للعرض
        prompt += `\n\nيرجى تنسيق الإجابة بصيغة HTML مع استخدام العناصر التالية:
- <h4> للعناوين الرئيسية
- <h5> للعناوين الفرعية
- <p> للفقرات
- <ul> و <li> للقوائم
- <strong> للنص المهم
- <div class="alert alert-info"> للمعلومات المهمة
- <div class="alert alert-warning"> للتحذيرات
- <div class="alert alert-success"> للتوصيات الإيجابية`;

        try {
            if (progressCallback) progressCallback(25);
            
            const analysis = await this.sendRequest(prompt);
            
            if (progressCallback) progressCallback(75);
            
            // طلب إضافي للحصول على توصيات محددة
            const optimizationPrompt = `
بناءً على التحليل السابق، قدم توصيات محددة لتحسين جدول المراهنات:

1. القيم المثلى للرهانات في كل جولة
2. النقاط الحرجة التي يجب الانتباه إليها
3. استراتيجيات بديلة في حالة الخسارة المتتالية
4. نصائح لإدارة المخاطر

قدم الإجابة في شكل نقاط واضحة ومحددة.
            `;
            
            const recommendations = await this.sendRequest(optimizationPrompt);
            
            if (progressCallback) progressCallback(100);
            
            return {
                analysis: analysis,
                recommendations: recommendations,
                timestamp: new Date().toISOString()
            };
            
        } catch (error) {
            throw new Error(`فشل في توليد التحليل: ${error.message}`);
        }
    }

    /**
     * تحليل المخاطر بالذكاء الصناعي
     */
    async analyzeRisks(tableData) {
        const prompt = `
قم بتحليل المخاطر للجدول التالي:

إحصائيات الجدول:
- إجمالي الجولات: ${tableData.statistics.totalRounds}
- إجمالي الرهانات: ${tableData.statistics.totalBets} جنيه
- متوسط الرهان: ${tableData.statistics.averageBet} جنيه
- أعلى رهان: ${tableData.statistics.maxBet} جنيه
- نسبة استخدام الرصيد: ${tableData.statistics.budgetUtilization}%
- مستوى المخاطرة: ${tableData.statistics.riskLevel}

المطلوب:
1. تقييم مستوى المخاطرة الإجمالي
2. تحديد النقاط الحرجة في الجدول
3. اقتراح تحسينات لتقليل المخاطر
4. تقديم سيناريوهات بديلة

قدم تحليلاً مفصلاً ونصائح عملية.
        `;

        return await this.sendRequest(prompt);
    }

    /**
     * تأخير التنفيذ
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * الحصول على إحصائيات الاستخدام
     */
    getUsageStats() {
        return {
            totalKeys: this.apiKeys.length,
            currentKeyIndex: this.currentKeyIndex,
            requestCounts: [...this.requestCounts],
            lastRequestTimes: [...this.lastRequestTime],
            availableKeys: this.apiKeys.filter((_, index) => {
                const timeSinceLastRequest = Date.now() - this.lastRequestTime[index];
                return timeSinceLastRequest > 60000 || this.requestCounts[index] < this.maxRequestsPerMinute;
            }).length
        };
    }

    /**
     * إعادة تعيين عدادات الاستخدام
     */
    resetUsageCounters() {
        this.requestCounts.fill(0);
        this.lastRequestTime.fill(0);
        this.currentKeyIndex = 0;
    }
}

// تصدير الكلاس للاستخدام العام
window.GeminiAIIntegration = GeminiAIIntegration;
