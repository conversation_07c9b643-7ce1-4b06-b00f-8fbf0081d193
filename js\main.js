/**
 * ===== الملف الرئيسي للتطبيق =====
 * تنسيق وإدارة جميع مكونات التطبيق
 */

class RocketBettingApp {
    constructor() {
        // تهيئة المكونات الأساسية
        this.calculator = new RocketBettingCalculator();
        this.aiIntegration = new GeminiAIIntegration();
        this.exportSystem = new ExportSystem();
        this.settingsManager = new SettingsManager();
        
        // متغيرات الحالة
        this.isGenerating = false;
        this.isPaused = false;
        this.currentProgress = 0;
        this.generationAborted = false;
        
        // تهيئة واجهة المستخدم
        this.initializeUI();
        this.bindEvents();
        this.loadInitialSettings();
    }

    /**
     * تهيئة واجهة المستخدم
     */
    initializeUI() {
        // إخفاء أقسام النتائج والتقدم في البداية
        document.getElementById('progressSection').style.display = 'none';
        document.getElementById('resultsSection').style.display = 'none';
        
        // تحديث واجهة المستخدم بالإعدادات الحالية
        this.updateUIWithSettings();
        
        // إضافة زر العودة للأعلى
        this.addScrollToTopButton();
    }

    /**
     * ربط الأحداث
     */
    bindEvents() {
        // زر توليد الجدول
        document.getElementById('generateTable').addEventListener('click', () => {
            this.generateTable();
        });
        
        // أزرار التحكم في التقدم
        document.getElementById('pauseGeneration').addEventListener('click', () => {
            this.togglePause();
        });
        
        document.getElementById('stopGeneration').addEventListener('click', () => {
            this.stopGeneration();
        });
        
        // أزرار التصدير
        document.getElementById('exportExcel').addEventListener('click', () => {
            this.exportData('excel');
        });
        
        document.getElementById('exportPDF').addEventListener('click', () => {
            this.exportData('pdf');
        });
        
        document.getElementById('exportTXT').addEventListener('click', () => {
            this.exportData('txt');
        });
        
        document.getElementById('exportHTML').addEventListener('click', () => {
            this.exportData('html');
        });
        
        // أزرار الإعدادات
        document.getElementById('exportSettings').addEventListener('click', () => {
            this.exportSettings();
        });
        
        document.getElementById('importSettings').addEventListener('click', () => {
            this.importSettings();
        });
        
        // مراقبة تغييرات الإعدادات
        this.bindSettingsEvents();
        
        // البحث في الجدول
        document.getElementById('searchTable').addEventListener('keyup', (e) => {
            this.searchTable(e.target.value);
        });
    }

    /**
     * ربط أحداث الإعدادات
     */
    bindSettingsEvents() {
        const settingsInputs = [
            'totalBudget', 'targetRounds', 'ratioIncrease', 
            'startingRatio', 'initialBet', 'betIncrement', 'aiMode'
        ];
        
        settingsInputs.forEach(inputId => {
            const element = document.getElementById(inputId);
            if (element) {
                element.addEventListener('change', () => {
                    this.updateSettingsFromUI();
                });
            }
        });
    }

    /**
     * تحميل الإعدادات الأولية
     */
    loadInitialSettings() {
        const settings = this.settingsManager.getCurrentSettings();
        this.calculator.updateSettings(settings);
        this.updateUIWithSettings();
    }

    /**
     * تحديث واجهة المستخدم بالإعدادات
     */
    updateUIWithSettings() {
        // استخدام دالة SettingsManager المحدثة
        this.settingsManager.updateFormFields();
    }

    /**
     * تحديث الإعدادات من واجهة المستخدم
     */
    updateSettingsFromUI() {
        // استخدام دالة SettingsManager المحدثة
        const newSettings = this.settingsManager.collectFormSettings();
        
        const validation = this.settingsManager.updateSettings(newSettings);
        this.calculator.updateSettings(newSettings);
        
        // عرض رسائل التحقق
        this.displayValidationMessages(validation);
    }

    /**
     * عرض رسائل التحقق
     */
    displayValidationMessages(validation) {
        // إزالة الرسائل السابقة
        const existingAlerts = document.querySelectorAll('.validation-alert');
        existingAlerts.forEach(alert => alert.remove());
        
        const container = document.querySelector('.input-section .card-body');
        
        // عرض الأخطاء
        if (validation.errors && validation.errors.length > 0) {
            const errorAlert = this.createAlert('danger', 'أخطاء في الإعدادات:', validation.errors);
            container.appendChild(errorAlert);
        }
        
        // عرض التحذيرات
        if (validation.warnings && validation.warnings.length > 0) {
            const warningAlert = this.createAlert('warning', 'تحذيرات:', validation.warnings);
            container.appendChild(warningAlert);
        }
    }

    /**
     * إنشاء تنبيه
     */
    createAlert(type, title, messages) {
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} validation-alert mt-3`;
        alert.innerHTML = `
            <strong>${title}</strong>
            <ul class="mb-0 mt-2">
                ${messages.map(msg => `<li>${msg}</li>`).join('')}
            </ul>
        `;
        return alert;
    }

    /**
     * توليد الجدول
     */
    async generateTable() {
        if (this.isGenerating) return;
        
        try {
            this.isGenerating = true;
            this.generationAborted = false;
            this.showProgressSection();
            
            // تحديث الإعدادات
            this.updateSettingsFromUI();
            
            // التحقق من صحة الإعدادات
            const validation = this.settingsManager.validateSettings();
            if (!validation.isValid) {
                throw new Error('الإعدادات غير صحيحة: ' + validation.errors.join(', '));
            }
            
            // بدء التوليد
            this.updateProgress(10, 'تحليل الإعدادات...');
            await this.delay(500);
            
            if (this.generationAborted) return;
            
            // توليد الجدول الأساسي
            this.updateProgress(30, 'حساب الجدول الأساسي...');
            const basicResult = this.calculator.generateTable();

            if (!basicResult.isValid) {
                throw new Error('فشل في توليد الجدول: ' + basicResult.errors.join(', '));
            }

            // تطبيق التعديل التلقائي إذا كان مفعلاً
            if (this.settingsManager.getCurrentSettings().autoAdjustment) {
                this.updateProgress(35, 'فحص الحاجة للتعديل التلقائي...');
                const adjustmentResult = this.calculator.autoAdjustSettings(basicResult.table);
                if (adjustmentResult.adjusted) {
                    basicResult.autoAdjustment = adjustmentResult;
                    // إعادة حساب الجدول بالإعدادات المعدلة
                    const adjustedResult = this.calculator.generateTable();
                    if (adjustedResult.isValid) {
                        Object.assign(basicResult, adjustedResult);
                        basicResult.autoAdjustment = adjustmentResult;
                    }
                }
            }

            // فحص خطة الانسحاب
            if (this.settingsManager.getCurrentSettings().exitStrategy) {
                this.updateProgress(40, 'تقييم خطة الانسحاب...');
                const totalLosses = basicResult.table
                    .filter(round => round.netProfit <= 0)
                    .reduce((sum, round) => sum + Math.abs(round.netProfit), 0);

                const exitResult = this.calculator.applyExitStrategy(basicResult.table, totalLosses);
                if (exitResult.shouldExit) {
                    basicResult.exitStrategy = exitResult;
                }
            }

            await this.delay(500);
            if (this.generationAborted) return;
            
            // تحسين بالذكاء الصناعي
            this.updateProgress(60, 'تحسين الجدول بالذكاء الصناعي...');
            
            try {
                const aiAnalysis = await this.aiIntegration.generateOptimizedTable(
                    this.settingsManager.getCurrentSettings(),
                    (progress) => {
                        this.updateProgress(60 + (progress * 0.3), 'تحليل ذكي...');
                    }
                );
                
                basicResult.aiAnalysis = aiAnalysis;
            } catch (aiError) {
                console.warn('فشل التحليل بالذكاء الصناعي:', aiError.message);
                basicResult.aiAnalysis = {
                    analysis: 'لم يتم التحليل بالذكاء الصناعي',
                    recommendations: 'استخدم الجدول الأساسي المحسوب'
                };
            }
            
            if (this.generationAborted) return;
            
            // عرض النتائج
            this.updateProgress(100, 'اكتمل التوليد!');
            await this.delay(500);
            
            this.displayResults(basicResult);
            this.hideProgressSection();
            
        } catch (error) {
            this.showError('خطأ في التوليد: ' + error.message);
            this.hideProgressSection();
        } finally {
            this.isGenerating = false;
        }
    }

    /**
     * عرض النتائج
     */
    displayResults(results) {
        // عرض الجدول
        const tableContainer = document.getElementById('tableContainer');
        tableContainer.innerHTML = this.generateTableHTML(results.table);
        
        // عرض الإحصائيات
        const statsContainer = document.getElementById('summaryStats');
        statsContainer.innerHTML = this.generateStatsHTML(results.statistics);
        
        // إضافة تحليل الذكاء الصناعي إذا كان متوفراً
        if (results.aiAnalysis) {
            this.addAIAnalysisSection(results.aiAnalysis);
        }

        // إضافة معلومات التعديل التلقائي إذا حدث
        if (results.autoAdjustment && results.autoAdjustment.adjusted) {
            this.addAutoAdjustmentSection(results.autoAdjustment);
        }

        // إضافة معلومات خطة الانسحاب إذا كانت مفعلة
        if (results.exitStrategy && results.exitStrategy.shouldExit) {
            this.addExitStrategySection(results.exitStrategy);
        }

        // إظهار قسم النتائج
        document.getElementById('resultsSection').style.display = 'block';
        
        // التمرير إلى النتائج
        document.getElementById('resultsSection').scrollIntoView({ 
            behavior: 'smooth' 
        });
        
        // حفظ النتائج للتصدير
        this.currentResults = results;
    }

    /**
     * إنشاء HTML للجدول
     */
    generateTableHTML(tableData) {
        let html = `
        <table class="table table-striped table-hover">
            <thead class="table-dark">
                <tr>
                    <th>الجولة</th>
                    <th>النسبة</th>
                    <th>الرهان (جنيه)</th>
                    <th>مجموع الخسائر السابقة</th>
                    <th>الربح عند الفوز</th>
                    <th>الربح الصافي</th>
                    <th>الحالة</th>
                </tr>
            </thead>
            <tbody>
        `;
        
        tableData.forEach(row => {
            const statusIcon = row.isWinning ? '✅' : '❌';
            const rowClass = row.isWinning ? 'table-success' : 'table-warning';
            const profitClass = row.netProfit > 0 ? 'text-success fw-bold' : 'text-danger fw-bold';
            
            html += `
                <tr class="${rowClass}">
                    <td class="fw-bold">${row.round}</td>
                    <td class="text-info fw-bold">×${row.ratio}</td>
                    <td class="text-warning fw-bold">${row.bet.toLocaleString()}</td>
                    <td>${row.previousLosses.toLocaleString()}</td>
                    <td class="text-primary fw-bold">${row.winAmount.toLocaleString()}</td>
                    <td class="${profitClass}">${row.netProfit > 0 ? '+' : ''}${row.netProfit.toLocaleString()}</td>
                    <td class="text-center">${statusIcon}</td>
                </tr>
            `;
        });
        
        html += '</tbody></table>';
        return html;
    }

    /**
     * إنشاء HTML للإحصائيات
     */
    generateStatsHTML(stats) {
        return `
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon text-primary">
                    <i class="fas fa-list-ol"></i>
                </div>
                <div class="stat-number">${stats.totalRounds}</div>
                <div class="stat-description">إجمالي الجولات</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon text-warning">
                    <i class="fas fa-coins"></i>
                </div>
                <div class="stat-number">${stats.totalBets.toLocaleString()}</div>
                <div class="stat-description">إجمالي الرهانات (جنيه)</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon text-info">
                    <i class="fas fa-chart-bar"></i>
                </div>
                <div class="stat-number">${stats.averageBet.toLocaleString()}</div>
                <div class="stat-description">متوسط الرهان (جنيه)</div>
            </div>
            
            <div class="stat-card success">
                <div class="stat-icon text-success">
                    <i class="fas fa-trophy"></i>
                </div>
                <div class="stat-number">${stats.winningPercentage.toFixed(1)}%</div>
                <div class="stat-description">نسبة الفوز</div>
            </div>
            
            <div class="stat-card ${this.getRiskColorClass(stats.riskLevel)}">
                <div class="stat-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stat-number">${stats.riskLevel}</div>
                <div class="stat-description">مستوى المخاطرة</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon text-success">
                    <i class="fas fa-wallet"></i>
                </div>
                <div class="stat-number">${stats.remainingBudget.toLocaleString()}</div>
                <div class="stat-description">الرصيد المتبقي (جنيه)</div>
            </div>
        </div>
        `;
    }

    /**
     * الحصول على فئة لون المخاطرة
     */
    getRiskColorClass(riskLevel) {
        switch (riskLevel) {
            case 'منخفض':
            case 'آمن':
                return 'success';
            case 'متوسط':
                return 'warning';
            case 'عالي':
            case 'خطر جداً':
                return 'danger';
            default:
                return '';
        }
    }

    /**
     * إضافة قسم تحليل الذكاء الصناعي
     */
    addAIAnalysisSection(aiAnalysis) {
        const analysisHTML = `
        <div class="ai-analysis-section mt-4">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-robot me-2"></i>
                        تحليل الذكاء الصناعي
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>التحليل:</h6>
                            <div class="ai-content">${aiAnalysis.analysis}</div>
                        </div>
                        <div class="col-md-6">
                            <h6>التوصيات:</h6>
                            <div class="ai-content">${aiAnalysis.recommendations}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        `;
        
        document.getElementById('summaryStats').insertAdjacentHTML('afterend', analysisHTML);
    }

    /**
     * البحث في الجدول
     */
    searchTable(searchTerm) {
        const table = document.querySelector('#tableContainer table');
        if (!table) return;
        
        const rows = table.querySelectorAll('tbody tr');
        const term = searchTerm.toLowerCase();
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(term) ? '' : 'none';
        });
    }

    /**
     * تصدير البيانات
     */
    async exportData(format) {
        if (!this.currentResults) {
            this.showError('لا توجد بيانات للتصدير. يرجى توليد الجدول أولاً.');
            return;
        }
        
        try {
            const result = await this.exportSystem.exportData(this.currentResults, format);
            this.showSuccess(`تم تصدير الملف بنجاح: ${result.filename}`);
        } catch (error) {
            this.showError('خطأ في التصدير: ' + error.message);
        }
    }

    /**
     * تصدير الإعدادات
     */
    exportSettings() {
        try {
            const result = this.settingsManager.exportSettings();
            this.showSuccess(`تم تصدير الإعدادات بنجاح: ${result.filename}`);
        } catch (error) {
            this.showError('خطأ في تصدير الإعدادات: ' + error.message);
        }
    }

    /**
     * استيراد الإعدادات
     */
    importSettings() {
        const fileInput = document.getElementById('settingsFileInput');
        fileInput.click();
        
        fileInput.onchange = async (e) => {
            const file = e.target.files[0];
            if (!file) return;
            
            try {
                const result = await this.settingsManager.importSettings(file);
                this.updateUIWithSettings();
                this.showSuccess('تم استيراد الإعدادات بنجاح');
                
                if (result.warnings && result.warnings.length > 0) {
                    console.warn('تحذيرات:', result.warnings);
                }
            } catch (error) {
                this.showError('خطأ في استيراد الإعدادات: ' + error.message);
            }
        };
    }

    /**
     * إظهار قسم التقدم
     */
    showProgressSection() {
        document.getElementById('progressSection').style.display = 'block';
        document.getElementById('resultsSection').style.display = 'none';
    }

    /**
     * إخفاء قسم التقدم
     */
    hideProgressSection() {
        document.getElementById('progressSection').style.display = 'none';
    }

    /**
     * تحديث شريط التقدم
     */
    updateProgress(percentage, status) {
        const progressBar = document.getElementById('progressBar');
        const progressStatus = document.getElementById('progressStatus');
        
        progressBar.style.width = percentage + '%';
        progressBar.setAttribute('aria-valuenow', percentage);
        progressStatus.textContent = status;
        
        this.currentProgress = percentage;
    }

    /**
     * إيقاف/استئناف التوليد
     */
    togglePause() {
        this.isPaused = !this.isPaused;
        const button = document.getElementById('pauseGeneration');
        
        if (this.isPaused) {
            button.innerHTML = '<i class="fas fa-play"></i> استئناف';
            button.className = 'btn btn-success me-2';
        } else {
            button.innerHTML = '<i class="fas fa-pause"></i> إيقاف مؤقت';
            button.className = 'btn btn-warning me-2';
        }
    }

    /**
     * إيقاف التوليد
     */
    stopGeneration() {
        this.generationAborted = true;
        this.isGenerating = false;
        this.hideProgressSection();
        this.showWarning('تم إيقاف عملية التوليد');
    }

    /**
     * إضافة زر العودة للأعلى
     */
    addScrollToTopButton() {
        const button = document.createElement('button');
        button.className = 'scroll-to-top';
        button.innerHTML = '<i class="fas fa-arrow-up"></i>';
        button.onclick = () => window.scrollTo({ top: 0, behavior: 'smooth' });
        document.body.appendChild(button);
        
        // إظهار/إخفاء الزر حسب التمرير
        window.addEventListener('scroll', () => {
            button.style.display = window.pageYOffset > 300 ? 'block' : 'none';
        });
    }

    /**
     * عرض رسالة نجاح
     */
    showSuccess(message) {
        this.showToast(message, 'success');
    }

    /**
     * عرض رسالة خطأ
     */
    showError(message) {
        this.showToast(message, 'error');
    }

    /**
     * عرض رسالة تحذير
     */
    showWarning(message) {
        this.showToast(message, 'warning');
    }

    /**
     * عرض رسالة منبثقة
     */
    showToast(message, type) {
        // إنشاء عنصر التنبيه
        const toast = document.createElement('div');
        toast.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        toast.style.cssText = 'top: 20px; left: 20px; z-index: 9999; max-width: 400px;';
        toast.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(toast);
        
        // إزالة التنبيه بعد 5 ثوان
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 5000);
    }

    /**
     * إضافة قسم التعديل التلقائي
     */
    addAutoAdjustmentSection(adjustmentData) {
        const container = document.getElementById('aiAnalysisContainer') ||
                         document.getElementById('resultsSection');

        const adjustmentHTML = `
            <div class="card mt-4 border-warning">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0">
                        <i class="fas fa-sync-alt me-2"></i>
                        تم التعديل التلقائي للإعدادات
                    </h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <strong>السبب:</strong> ${adjustmentData.reason}
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <strong>الرهان الأولي الجديد:</strong><br>
                            <span class="text-primary">${adjustmentData.changes.initialBet} جنيه</span>
                        </div>
                        <div class="col-md-4">
                            <strong>زيادة الرهان الجديدة:</strong><br>
                            <span class="text-primary">${adjustmentData.changes.betIncrement} جنيه</span>
                        </div>
                        <div class="col-md-4">
                            <strong>النسبة الأولى الجديدة:</strong><br>
                            <span class="text-primary">×${adjustmentData.changes.startingRatio}</span>
                        </div>
                    </div>
                </div>
            </div>
        `;

        container.insertAdjacentHTML('beforeend', adjustmentHTML);
    }

    /**
     * إضافة قسم خطة الانسحاب
     */
    addExitStrategySection(exitData) {
        const container = document.getElementById('aiAnalysisContainer') ||
                         document.getElementById('resultsSection');

        const exitHTML = `
            <div class="card mt-4 border-danger">
                <div class="card-header bg-danger text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-door-open me-2"></i>
                        خطة الانسحاب الذكية مفعلة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <strong>السبب:</strong> ${exitData.reason}
                    </div>
                    <div class="row">
                        <div class="col-md-3">
                            <strong>عدد جولات الانسحاب:</strong><br>
                            <span class="text-danger">${exitData.exitPlan.rounds} جولة</span>
                        </div>
                        <div class="col-md-3">
                            <strong>الهدف المعدل:</strong><br>
                            <span class="text-success">${exitData.exitPlan.targetProfit.toLocaleString()} جنيه</span>
                        </div>
                        <div class="col-md-3">
                            <strong>الرهان المقترح:</strong><br>
                            <span class="text-primary">${exitData.exitPlan.recommendedBet.toLocaleString()} جنيه</span>
                        </div>
                        <div class="col-md-3">
                            <strong>النسبة المعدلة:</strong><br>
                            <span class="text-info">×${exitData.exitPlan.adjustedRatio}</span>
                        </div>
                    </div>
                    <div class="mt-3">
                        <strong>الاستراتيجية:</strong> ${exitData.exitPlan.strategy}
                    </div>
                </div>
            </div>
        `;

        container.insertAdjacentHTML('beforeend', exitHTML);
    }

    /**
     * تأخير التنفيذ
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.rocketBettingApp = new RocketBettingApp();
});
