/**
 * ===== الملف الرئيسي للتطبيق =====
 * تنسيق وإدارة جميع مكونات التطبيق
 */

class RocketBettingApp {
    constructor() {
        // تهيئة المكونات الأساسية
        this.calculator = new RocketBettingCalculator();
        this.aiIntegration = new GeminiAIIntegration();
        this.exportSystem = new ExportSystem();
        this.settingsManager = new SettingsManager();
        
        // متغيرات الحالة
        this.isGenerating = false;
        this.isPaused = false;
        this.currentProgress = 0;
        this.generationAborted = false;
        
        // تهيئة واجهة المستخدم
        this.initializeUI();
        this.bindEvents();
        this.loadInitialSettings();
    }

    /**
     * تهيئة واجهة المستخدم
     */
    initializeUI() {
        // إخفاء أقسام النتائج والتقدم في البداية
        document.getElementById('progressSection').style.display = 'none';
        document.getElementById('resultsSection').style.display = 'none';
        
        // تحديث واجهة المستخدم بالإعدادات الحالية
        this.updateUIWithSettings();
        
        // إضافة زر العودة للأعلى
        this.addScrollToTopButton();
    }

    /**
     * ربط الأحداث
     */
    bindEvents() {
        // زر توليد الجدول
        document.getElementById('generateTable').addEventListener('click', () => {
            this.generateTable();
        });
        
        // أزرار التحكم في التقدم
        document.getElementById('pauseGeneration').addEventListener('click', () => {
            this.togglePause();
        });
        
        document.getElementById('stopGeneration').addEventListener('click', () => {
            this.stopGeneration();
        });
        
        // أزرار التصدير
        document.getElementById('exportExcel').addEventListener('click', () => {
            this.exportData('excel');
        });
        
        document.getElementById('exportPDF').addEventListener('click', () => {
            this.exportData('pdf');
        });
        
        document.getElementById('exportTXT').addEventListener('click', () => {
            this.exportData('txt');
        });
        
        document.getElementById('exportHTML').addEventListener('click', () => {
            this.exportData('html');
        });
        
        // أزرار الإعدادات
        document.getElementById('exportSettings').addEventListener('click', () => {
            this.exportSettings();
        });
        
        document.getElementById('importSettings').addEventListener('click', () => {
            this.importSettings();
        });
        
        // مراقبة تغييرات الإعدادات
        this.bindSettingsEvents();
        
        // البحث في الجدول
        document.getElementById('searchTable').addEventListener('keyup', (e) => {
            this.searchTable(e.target.value);
        });
    }

    /**
     * ربط أحداث الإعدادات
     */
    bindSettingsEvents() {
        const settingsInputs = [
            'totalBudget', 'targetRounds', 'ratioIncrease', 
            'startingRatio', 'initialBet', 'betIncrement', 'aiMode'
        ];
        
        settingsInputs.forEach(inputId => {
            const element = document.getElementById(inputId);
            if (element) {
                element.addEventListener('change', () => {
                    this.updateSettingsFromUI();
                });
            }
        });
    }

    /**
     * تحميل الإعدادات الأولية
     */
    loadInitialSettings() {
        const settings = this.settingsManager.getCurrentSettings();
        this.calculator.updateSettings(settings);
        this.updateUIWithSettings();
    }

    /**
     * تحديث واجهة المستخدم بالإعدادات
     */
    updateUIWithSettings() {
        const settings = this.settingsManager.getCurrentSettings();
        
        document.getElementById('totalBudget').value = settings.totalBudget;
        document.getElementById('targetRounds').value = settings.targetRounds;
        document.getElementById('ratioIncrease').value = settings.ratioIncrease;
        document.getElementById('startingRatio').value = settings.startingRatio;
        document.getElementById('initialBet').value = settings.initialBet;
        document.getElementById('betIncrement').value = settings.betIncrement;
        document.getElementById('aiMode').value = settings.aiMode;
        document.getElementById('reinvestmentEnabled').checked = settings.reinvestmentEnabled;
        document.getElementById('reinvestmentStartRound').value = settings.reinvestmentStartRound;
        document.getElementById('reinvestmentPercentage').value = settings.reinvestmentPercentage;
    }

    /**
     * تحديث الإعدادات من واجهة المستخدم
     */
    updateSettingsFromUI() {
        const newSettings = {
            totalBudget: parseInt(document.getElementById('totalBudget').value),
            targetRounds: parseInt(document.getElementById('targetRounds').value),
            ratioIncrease: parseInt(document.getElementById('ratioIncrease').value),
            startingRatio: parseFloat(document.getElementById('startingRatio').value),
            initialBet: parseInt(document.getElementById('initialBet').value),
            betIncrement: parseInt(document.getElementById('betIncrement').value),
            aiMode: document.getElementById('aiMode').value,
            reinvestmentEnabled: document.getElementById('reinvestmentEnabled').checked,
            reinvestmentStartRound: parseInt(document.getElementById('reinvestmentStartRound').value),
            reinvestmentPercentage: parseInt(document.getElementById('reinvestmentPercentage').value)
        };
        
        const validation = this.settingsManager.updateSettings(newSettings);
        this.calculator.updateSettings(newSettings);
        
        // عرض رسائل التحقق
        this.displayValidationMessages(validation);
    }

    /**
     * عرض رسائل التحقق
     */
    displayValidationMessages(validation) {
        // إزالة الرسائل السابقة
        const existingAlerts = document.querySelectorAll('.validation-alert');
        existingAlerts.forEach(alert => alert.remove());
        
        const container = document.querySelector('.input-section .card-body');
        
        // عرض الأخطاء
        if (validation.errors && validation.errors.length > 0) {
            const errorAlert = this.createAlert('danger', 'أخطاء في الإعدادات:', validation.errors);
            container.appendChild(errorAlert);
        }
        
        // عرض التحذيرات
        if (validation.warnings && validation.warnings.length > 0) {
            const warningAlert = this.createAlert('warning', 'تحذيرات:', validation.warnings);
            container.appendChild(warningAlert);
        }
    }

    /**
     * إنشاء تنبيه
     */
    createAlert(type, title, messages) {
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} validation-alert mt-3`;
        alert.innerHTML = `
            <strong>${title}</strong>
            <ul class="mb-0 mt-2">
                ${messages.map(msg => `<li>${msg}</li>`).join('')}
            </ul>
        `;
        return alert;
    }

    /**
     * توليد الجدول
     */
    async generateTable() {
        if (this.isGenerating) return;
        
        try {
            this.isGenerating = true;
            this.generationAborted = false;
            this.showProgressSection();
            
            // تحديث الإعدادات
            this.updateSettingsFromUI();
            
            // التحقق من صحة الإعدادات
            const validation = this.settingsManager.validateSettings();
            if (!validation.isValid) {
                throw new Error('الإعدادات غير صحيحة: ' + validation.errors.join(', '));
            }
            
            // بدء التوليد
            this.updateProgress(10, 'تحليل الإعدادات...');
            await this.delay(500);
            
            if (this.generationAborted) return;
            
            // توليد الجدول الأساسي
            this.updateProgress(30, 'حساب الجدول الأساسي...');
            const basicResult = this.calculator.generateTable();
            
            if (!basicResult.isValid) {
                throw new Error('فشل في توليد الجدول: ' + basicResult.errors.join(', '));
            }
            
            await this.delay(500);
            if (this.generationAborted) return;
            
            // تحسين بالذكاء الصناعي
            this.updateProgress(60, 'تحسين الجدول بالذكاء الصناعي...');
            
            try {
                const aiAnalysis = await this.aiIntegration.generateOptimizedTable(
                    this.settingsManager.getCurrentSettings(),
                    (progress) => {
                        this.updateProgress(60 + (progress * 0.3), 'تحليل ذكي...');
                    }
                );
                
                basicResult.aiAnalysis = aiAnalysis;
            } catch (aiError) {
                console.warn('فشل التحليل بالذكاء الصناعي:', aiError.message);
                basicResult.aiAnalysis = {
                    analysis: 'لم يتم التحليل بالذكاء الصناعي',
                    recommendations: 'استخدم الجدول الأساسي المحسوب'
                };
            }
            
            if (this.generationAborted) return;
            
            // عرض النتائج
            this.updateProgress(100, 'اكتمل التوليد!');
            await this.delay(500);
            
            this.displayResults(basicResult);
            this.hideProgressSection();
            
        } catch (error) {
            this.showError('خطأ في التوليد: ' + error.message);
            this.hideProgressSection();
        } finally {
            this.isGenerating = false;
        }
    }

    /**
     * عرض النتائج
     */
    displayResults(results) {
        // عرض الجدول
        const tableContainer = document.getElementById('tableContainer');
        tableContainer.innerHTML = this.generateTableHTML(results.table);
        
        // عرض الإحصائيات
        const statsContainer = document.getElementById('summaryStats');
        statsContainer.innerHTML = this.generateStatsHTML(results.statistics);
        
        // إضافة تحليل الذكاء الصناعي إذا كان متوفراً
        if (results.aiAnalysis) {
            this.addAIAnalysisSection(results.aiAnalysis);
        }
        
        // إظهار قسم النتائج
        document.getElementById('resultsSection').style.display = 'block';
        
        // التمرير إلى النتائج
        document.getElementById('resultsSection').scrollIntoView({ 
            behavior: 'smooth' 
        });
        
        // حفظ النتائج للتصدير
        this.currentResults = results;
    }

    /**
     * إنشاء HTML للجدول
     */
    generateTableHTML(tableData) {
        let html = `
        <table class="table table-striped table-hover">
            <thead class="table-dark">
                <tr>
                    <th>الجولة</th>
                    <th>النسبة</th>
                    <th>الرهان (جنيه)</th>
                    <th>مجموع الخسائر السابقة</th>
                    <th>الربح عند الفوز</th>
                    <th>الربح الصافي</th>
                    <th>إعادة الاستثمار</th>
                    <th>الحالة</th>
                </tr>
            </thead>
            <tbody>
        `;

        tableData.forEach(row => {
            const statusIcon = row.isWinning ? '✅' : '❌';
            const rowClass = row.isWinning ? 'table-success' : 'table-warning';
            const profitClass = row.netProfit > 0 ? 'text-success fw-bold' : 'text-danger fw-bold';

            // تحديد معلومات إعادة الاستثمار
            let reinvestmentInfo = '-';
            if (row.isReinvestmentRound && row.reinvestmentAmount > 0) {
                reinvestmentInfo = `<span class="badge bg-info">+${row.reinvestmentAmount.toLocaleString()} جنيه</span>`;
            }

            html += `
                <tr class="${rowClass}">
                    <td class="fw-bold">${row.round}</td>
                    <td class="text-info fw-bold">×${row.ratio}</td>
                    <td class="text-warning fw-bold">${row.bet.toLocaleString()}</td>
                    <td>${row.previousLosses.toLocaleString()}</td>
                    <td class="text-primary fw-bold">${row.winAmount.toLocaleString()}</td>
                    <td class="${profitClass}">${row.netProfit > 0 ? '+' : ''}${row.netProfit.toLocaleString()}</td>
                    <td class="text-center">${reinvestmentInfo}</td>
                    <td class="text-center">${statusIcon}</td>
                </tr>
            `;
        });

        html += '</tbody></table>';
        return html;
    }

    /**
     * إنشاء HTML للإحصائيات
     */
    generateStatsHTML(stats) {
        return `
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon text-primary">
                    <i class="fas fa-list-ol"></i>
                </div>
                <div class="stat-number">${stats.totalRounds}</div>
                <div class="stat-description">إجمالي الجولات</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon text-warning">
                    <i class="fas fa-coins"></i>
                </div>
                <div class="stat-number">${stats.totalBets.toLocaleString()}</div>
                <div class="stat-description">إجمالي الرهانات (جنيه)</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon text-info">
                    <i class="fas fa-chart-bar"></i>
                </div>
                <div class="stat-number">${stats.averageBet.toLocaleString()}</div>
                <div class="stat-description">متوسط الرهان (جنيه)</div>
            </div>
            
            <div class="stat-card success">
                <div class="stat-icon text-success">
                    <i class="fas fa-trophy"></i>
                </div>
                <div class="stat-number">${stats.winningPercentage.toFixed(1)}%</div>
                <div class="stat-description">نسبة الفوز</div>
            </div>
            
            <div class="stat-card ${this.getRiskColorClass(stats.riskLevel)}">
                <div class="stat-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stat-number">${stats.riskLevel}</div>
                <div class="stat-description">مستوى المخاطرة</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon text-success">
                    <i class="fas fa-wallet"></i>
                </div>
                <div class="stat-number">${stats.remainingBudget.toLocaleString()}</div>
                <div class="stat-description">الرصيد المتبقي (جنيه)</div>
            </div>

            ${stats.reinvestmentEnabled ? `
            <div class="stat-card info">
                <div class="stat-icon text-info">
                    <i class="fas fa-recycle"></i>
                </div>
                <div class="stat-number">${stats.totalReinvested.toLocaleString()}</div>
                <div class="stat-description">إجمالي إعادة الاستثمار (جنيه)</div>
            </div>

            <div class="stat-card success">
                <div class="stat-icon text-success">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stat-number">${stats.additionalProfits.toLocaleString()}</div>
                <div class="stat-description">أرباح إضافية من إعادة الاستثمار</div>
            </div>

            <div class="stat-card warning">
                <div class="stat-icon text-warning">
                    <i class="fas fa-percentage"></i>
                </div>
                <div class="stat-number">${stats.reinvestmentROI.toFixed(1)}%</div>
                <div class="stat-description">عائد إعادة الاستثمار</div>
            </div>
            ` : ''}
        </div>
        `;
    }

    /**
     * الحصول على فئة لون المخاطرة
     */
    getRiskColorClass(riskLevel) {
        switch (riskLevel) {
            case 'منخفض':
            case 'آمن':
                return 'success';
            case 'متوسط':
                return 'warning';
            case 'عالي':
            case 'خطر جداً':
                return 'danger';
            default:
                return '';
        }
    }

    /**
     * إضافة قسم تحليل الذكاء الصناعي
     */
    addAIAnalysisSection(aiAnalysis) {
        // تحويل النص إلى HTML منسق
        const formattedAnalysis = this.formatTextToHTML(aiAnalysis.analysis);
        const formattedRecommendations = this.formatTextToHTML(aiAnalysis.recommendations);

        const analysisHTML = `
        <div class="ai-analysis-section mt-4">
            <div class="card border-info">
                <div class="card-header bg-gradient text-white" style="background: linear-gradient(135deg, #17a2b8, #007bff);">
                    <h5 class="mb-0">
                        <i class="fas fa-robot me-2"></i>
                        تحليل الذكاء الصناعي المتقدم
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-6 mb-4">
                            <div class="analysis-section">
                                <h6 class="text-primary mb-3">
                                    <i class="fas fa-chart-line me-2"></i>
                                    التحليل التفصيلي:
                                </h6>
                                <div class="ai-content formatted-content">
                                    ${formattedAnalysis}
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 mb-4">
                            <div class="recommendations-section">
                                <h6 class="text-success mb-3">
                                    <i class="fas fa-lightbulb me-2"></i>
                                    التوصيات والنصائح:
                                </h6>
                                <div class="ai-content formatted-content">
                                    ${formattedRecommendations}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>ملاحظة:</strong> هذا التحليل مُولد بواسطة الذكاء الصناعي ويعتمد على البيانات المُدخلة. يُنصح بمراجعة النتائج مع خبير مالي قبل اتخاذ قرارات استثمارية.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        `;

        document.getElementById('summaryStats').insertAdjacentHTML('afterend', analysisHTML);
    }

    /**
     * تحويل النص إلى HTML منسق
     */
    formatTextToHTML(text) {
        if (!text) return '';

        // تقسيم النص إلى فقرات
        let formattedText = text
            .split('\n')
            .filter(line => line.trim())
            .map(line => {
                line = line.trim();

                // تنسيق العناوين (النصوص التي تنتهي بـ :)
                if (line.endsWith(':')) {
                    return `<h6 class="text-primary mt-3 mb-2"><i class="fas fa-arrow-left me-2"></i>${line}</h6>`;
                }

                // تنسيق النقاط (النصوص التي تبدأ بـ - أو •)
                if (line.startsWith('-') || line.startsWith('•')) {
                    const content = line.substring(1).trim();
                    return `<div class="mb-2"><i class="fas fa-check-circle text-success me-2"></i>${content}</div>`;
                }

                // تنسيق الأرقام (النصوص التي تبدأ برقم.)
                if (/^\d+\./.test(line)) {
                    return `<div class="mb-2"><span class="badge bg-primary me-2">${line.match(/^\d+/)[0]}</span>${line.substring(line.indexOf('.') + 1).trim()}</div>`;
                }

                // فقرة عادية
                return `<p class="mb-2">${line}</p>`;
            })
            .join('');

        // تمييز الكلمات المهمة
        formattedText = formattedText
            .replace(/(\d+%)/g, '<span class="badge bg-warning text-dark">$1</span>')
            .replace(/(\d+\s*جنيه)/g, '<span class="text-success fw-bold">$1</span>')
            .replace(/(مخاطرة|خطر|احتياط)/g, '<span class="text-danger fw-bold">$1</span>')
            .replace(/(ربح|فائدة|مكسب)/g, '<span class="text-success fw-bold">$1</span>')
            .replace(/(توصية|نصيحة|يُنصح)/g, '<span class="text-info fw-bold">$1</span>');

        return formattedText;
    }

    /**
     * البحث في الجدول
     */
    searchTable(searchTerm) {
        const table = document.querySelector('#tableContainer table');
        if (!table) return;
        
        const rows = table.querySelectorAll('tbody tr');
        const term = searchTerm.toLowerCase();
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(term) ? '' : 'none';
        });
    }

    /**
     * تصدير البيانات
     */
    async exportData(format) {
        if (!this.currentResults) {
            this.showError('لا توجد بيانات للتصدير. يرجى توليد الجدول أولاً.');
            return;
        }
        
        try {
            const result = await this.exportSystem.exportData(this.currentResults, format);
            this.showSuccess(`تم تصدير الملف بنجاح: ${result.filename}`);
        } catch (error) {
            this.showError('خطأ في التصدير: ' + error.message);
        }
    }

    /**
     * تصدير الإعدادات
     */
    exportSettings() {
        try {
            const result = this.settingsManager.exportSettings();
            this.showSuccess(`تم تصدير الإعدادات بنجاح: ${result.filename}`);
        } catch (error) {
            this.showError('خطأ في تصدير الإعدادات: ' + error.message);
        }
    }

    /**
     * استيراد الإعدادات
     */
    importSettings() {
        const fileInput = document.getElementById('settingsFileInput');
        fileInput.click();
        
        fileInput.onchange = async (e) => {
            const file = e.target.files[0];
            if (!file) return;
            
            try {
                const result = await this.settingsManager.importSettings(file);
                this.updateUIWithSettings();
                this.showSuccess('تم استيراد الإعدادات بنجاح');
                
                if (result.warnings && result.warnings.length > 0) {
                    console.warn('تحذيرات:', result.warnings);
                }
            } catch (error) {
                this.showError('خطأ في استيراد الإعدادات: ' + error.message);
            }
        };
    }

    /**
     * إظهار قسم التقدم
     */
    showProgressSection() {
        document.getElementById('progressSection').style.display = 'block';
        document.getElementById('resultsSection').style.display = 'none';
    }

    /**
     * إخفاء قسم التقدم
     */
    hideProgressSection() {
        document.getElementById('progressSection').style.display = 'none';
    }

    /**
     * تحديث شريط التقدم
     */
    updateProgress(percentage, status) {
        const progressBar = document.getElementById('progressBar');
        const progressStatus = document.getElementById('progressStatus');
        
        progressBar.style.width = percentage + '%';
        progressBar.setAttribute('aria-valuenow', percentage);
        progressStatus.textContent = status;
        
        this.currentProgress = percentage;
    }

    /**
     * إيقاف/استئناف التوليد
     */
    togglePause() {
        this.isPaused = !this.isPaused;
        const button = document.getElementById('pauseGeneration');
        
        if (this.isPaused) {
            button.innerHTML = '<i class="fas fa-play"></i> استئناف';
            button.className = 'btn btn-success me-2';
        } else {
            button.innerHTML = '<i class="fas fa-pause"></i> إيقاف مؤقت';
            button.className = 'btn btn-warning me-2';
        }
    }

    /**
     * إيقاف التوليد
     */
    stopGeneration() {
        this.generationAborted = true;
        this.isGenerating = false;
        this.hideProgressSection();
        this.showWarning('تم إيقاف عملية التوليد');
    }

    /**
     * إضافة زر العودة للأعلى
     */
    addScrollToTopButton() {
        const button = document.createElement('button');
        button.className = 'scroll-to-top';
        button.innerHTML = '<i class="fas fa-arrow-up"></i>';
        button.onclick = () => window.scrollTo({ top: 0, behavior: 'smooth' });
        document.body.appendChild(button);
        
        // إظهار/إخفاء الزر حسب التمرير
        window.addEventListener('scroll', () => {
            button.style.display = window.pageYOffset > 300 ? 'block' : 'none';
        });
    }

    /**
     * عرض رسالة نجاح
     */
    showSuccess(message) {
        this.showToast(message, 'success');
    }

    /**
     * عرض رسالة خطأ
     */
    showError(message) {
        this.showToast(message, 'error');
    }

    /**
     * عرض رسالة تحذير
     */
    showWarning(message) {
        this.showToast(message, 'warning');
    }

    /**
     * عرض رسالة منبثقة
     */
    showToast(message, type) {
        // إنشاء عنصر التنبيه
        const toast = document.createElement('div');
        toast.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        toast.style.cssText = 'top: 20px; left: 20px; z-index: 9999; max-width: 400px;';
        toast.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(toast);
        
        // إزالة التنبيه بعد 5 ثوان
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 5000);
    }

    /**
     * تأخير التنفيذ
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.rocketBettingApp = new RocketBettingApp();
});
